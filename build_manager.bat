@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    🏗️ مدير بناء APK - Build Manager
echo    محاسب ديون احترافي
echo ========================================
echo.

REM التحقق من وجود Flutter
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Flutter أولاً
    pause
    exit /b 1
)

echo ✅ Flutter متوفر
echo.

REM عرض معلومات APK الحالي
if exist "محاسب_ديون_احترافي.apk" (
    echo 📱 APK الحالي:
    for %%A in ("محاسب_ديون_احترافي.apk") do (
        set /a size_mb=%%~zA/1024/1024
        echo    📁 الحجم: !size_mb! MB
        echo    📅 آخر تعديل: %%~tA
    )
    echo.
)

echo 🔧 خيارات البناء المتاحة:
echo.
echo [1] 🏗️  بناء APK كامل (مُحسن ومضغوط)
echo [2] ⚡ بناء APK سريع (للتطوير)
echo [3] 👁️  مراقبة الملفات وبناء تلقائي
echo [4] 🧹 تنظيف وبناء من الصفر
echo [5] 📊 معلومات المشروع
echo [6] 🚀 تشغيل التطبيق للاختبار
echo [7] 📦 إنشاء نسخة للتوزيع
echo [0] ❌ خروج
echo.

set /p choice="اختر رقم الخيار (0-7): "

if "%choice%"=="1" goto full_build
if "%choice%"=="2" goto quick_build
if "%choice%"=="3" goto watch_build
if "%choice%"=="4" goto clean_build
if "%choice%"=="5" goto project_info
if "%choice%"=="6" goto run_app
if "%choice%"=="7" goto distribution_build
if "%choice%"=="0" goto exit
goto invalid_choice

:full_build
echo.
echo 🏗️ بدء البناء الكامل...
call scripts\auto_build_apk.bat
goto end

:quick_build
echo.
echo ⚡ بدء البناء السريع...
call scripts\quick_build_apk.bat
goto end

:watch_build
echo.
echo 👁️ بدء مراقبة الملفات...
call scripts\watch_and_build.bat
goto end

:clean_build
echo.
echo 🧹 تنظيف وبناء من الصفر...
echo تحذير: سيتم حذف جميع ملفات البناء السابقة
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%"=="y" (
    echo تنظيف الملفات...
    if exist "build" rmdir /s /q "build"
    if exist ".dart_tool" rmdir /s /q ".dart_tool"
    flutter clean
    flutter pub get
    call scripts\auto_build_apk.bat
) else (
    echo تم الإلغاء
)
goto end

:project_info
echo.
echo 📊 معلومات المشروع:
echo ==================
echo.
flutter --version
echo.
echo 📁 معلومات المشروع:
if exist "pubspec.yaml" (
    findstr /C:"name:" pubspec.yaml
    findstr /C:"version:" pubspec.yaml
    findstr /C:"description:" pubspec.yaml
)
echo.
echo 📱 معلومات APK:
if exist "محاسب_ديون_احترافي.apk" (
    for %%A in ("محاسب_ديون_احترافي.apk") do (
        set /a size_mb=%%~zA/1024/1024
        echo    📁 الحجم: !size_mb! MB (%%~zA bytes)
        echo    📅 تاريخ الإنشاء: %%~tA
    )
) else (
    echo    ❌ لا يوجد APK مبني
)
echo.
echo 📂 ملفات البناء:
if exist "build" (
    echo    ✅ مجلد build موجود
) else (
    echo    ❌ مجلد build غير موجود
)
echo.
pause
goto start

:run_app
echo.
echo 🚀 تشغيل التطبيق للاختبار...
echo.
echo اختر طريقة التشغيل:
echo [1] تشغيل عادي مع Hot Reload
echo [2] تشغيل Profile Mode
echo [3] تشغيل Release Mode
echo.
set /p run_choice="اختر (1-3): "

if "%run_choice%"=="1" (
    flutter run --hot
) else if "%run_choice%"=="2" (
    flutter run --profile
) else if "%run_choice%"=="3" (
    flutter run --release
) else (
    echo خيار غير صحيح
)
goto end

:distribution_build
echo.
echo 📦 إنشاء نسخة للتوزيع...
echo.

REM إنشاء مجلد التوزيع
if not exist "distribution" mkdir "distribution"

REM الحصول على التاريخ
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "date_str=%YYYY%-%MM%-%DD%"

echo بناء APK للتوزيع...
call scripts\auto_build_apk.bat

if exist "محاسب_ديون_احترافي.apk" (
    copy "محاسب_ديون_احترافي.apk" "distribution\محاسب_ديون_احترافي_%date_str%.apk"
    echo.
    echo ✅ تم إنشاء نسخة التوزيع:
    echo    📁 distribution\محاسب_ديون_احترافي_%date_str%.apk
    echo.
    echo 📋 ملاحظات للتوزيع:
    echo    - تأكد من اختبار APK على أجهزة مختلفة
    echo    - راجع الأذونات المطلوبة
    echo    - تحقق من التوافق مع إصدارات Android
) else (
    echo ❌ فشل في إنشاء APK للتوزيع
)
goto end

:invalid_choice
echo.
echo ❌ خيار غير صحيح، يرجى اختيار رقم من 0 إلى 7
echo.
pause
goto start

:start
cls
goto :eof

:end
echo.
echo 🔄 هل تريد العودة للقائمة الرئيسية؟ (y/n)
set /p return_choice=""
if /i "%return_choice%"=="y" (
    cls
    goto :eof
)

:exit
echo.
echo 👋 شكراً لاستخدام مدير بناء APK
echo.
