import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/notification_model.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/card_type_provider.dart';
import '../services/local_notification_service.dart';
import '../utils/number_formatter.dart';

class SmartNotificationProvider with ChangeNotifier {
  final List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  bool _isInitialized = false;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;

  // الحصول على التنبيهات غير المقروءة
  List<NotificationModel> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  // عدد التنبيهات غير المقروءة
  int get unreadCount => unreadNotifications.length;

  // الحصول على التنبيهات حسب النوع
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // الحصول على التنبيهات حسب العميل
  List<NotificationModel> getNotificationsByCustomer(String customerId) {
    return _notifications.where((n) => n.customerId == customerId).toList();
  }

  // إنشاء التنبيهات الذكية - نظام محسن يعتمد على المصدر
  Future<void> generateSmartNotifications({
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardInventoryProvider inventoryProvider,
    NotificationProvider? notificationProvider,
  }) async {
    debugPrint('🚀 بدء إنشاء الإشعارات الذكية من المصدر...');
    debugPrint('📊 عدد الديون: ${debtProvider.debts.length}');
    debugPrint('👥 عدد العملاء: ${customerProvider.customers.length}');

    if (_isLoading) {
      debugPrint('⚠️ التنبيهات قيد التحديث بالفعل، تجاهل الطلب');
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      // تحميل التنبيهات المحفوظة أولاً (نظام جوجل)
      if (!_isInitialized) {
        await initializeNotifications();
      }

      // الاحتفاظ بجميع التنبيهات الموجودة (مثل جوجل - لا تختفي أبداً)
      final existingNotifications =
          List<NotificationModel>.from(_notifications);
      debugPrint('🔄 الاحتفاظ بـ ${existingNotifications.length} تنبيه موجود');

      // إنشاء تنبيهات جديدة من المصدر مباشرة (بدون مسح القديمة)
      await _generateDebtNotificationsFromSource(
        debtProvider,
        customerProvider,
        notificationProvider,
      );

      // إنشاء تنبيهات المخزون من المصدر
      await _generateInventoryNotifications(inventoryProvider);

      // تنظيف التكرارات الذكي
      _cleanupDuplicateNotifications();

      // ترتيب التنبيهات حسب الأولوية والتاريخ (الأحدث أولاً مثل جوجل)
      _sortNotifications();

      // حفظ التنبيهات المحدثة في التخزين المحلي
      await _saveNotificationsToStorage();

      debugPrint('✅ تم تحديث التنبيهات - المجموع: ${_notifications.length}');
      debugPrint('🔔 غير مقروء: $unreadCount');
      debugPrint('📖 مقروء: ${_notifications.length - unreadCount}');
      debugPrint('💾 جميع التنبيهات محفوظة دائماً (نظام جوجل)');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التنبيهات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إنشاء تنبيهات الديون من المصدر مباشرة - نظام محسن
  Future<void> _generateDebtNotificationsFromSource(
    DebtProvider debtProvider,
    CustomerProvider customerProvider,
    NotificationProvider? notificationProvider,
  ) async {
    debugPrint('📋 إنشاء تنبيهات الديون من المصدر...');

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // إحصائيات للمتابعة
    int overdueCount = 0;
    int dueTodayCount = 0;
    int dueSoonCount = 0;

    // معالجة كل دين بشكل منفصل لضمان الدقة
    for (final debt in debtProvider.debts) {
      try {
        // البحث عن العميل من المصدر مباشرة
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => Customer(
            id: debt.customerId,
            name: 'عميل غير موجود',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );

        // تحديد نوع التنبيه بناءً على تاريخ الاستحقاق
        NotificationType? notificationType;
        NotificationPriority priority = NotificationPriority.low;
        String titlePrefix = '';

        if (dueDate.isBefore(today)) {
          // دين متأخر
          notificationType = NotificationType.overdue;
          priority = NotificationPriority.urgent;
          titlePrefix = 'دين متأخر';
          overdueCount++;
        } else if (dueDate.isAtSameMomentAs(today)) {
          // دين مستحق اليوم
          notificationType = NotificationType.dueToday;
          priority = NotificationPriority.high;
          titlePrefix = 'دين مستحق اليوم';
          dueTodayCount++;
        } else if (dueDate.isBefore(today.add(const Duration(days: 4)))) {
          // دين مستحق خلال 3 أيام
          notificationType = NotificationType.dueSoon;
          priority = NotificationPriority.medium;
          titlePrefix = 'دين مستحق قريباً';
          dueSoonCount++;
        }

        // إنشاء التنبيه إذا كان مطلوباً
        if (notificationType != null) {
          await _createIndividualDebtNotification(
            customer,
            debt,
            notificationType,
            priority,
            titlePrefix,
            now,
            1, // رقم الدين
            1, // إجمالي الديون
            notificationProvider,
          );
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة الدين ${debt.id}: $e');
      }
    }

    debugPrint('📊 إحصائيات التنبيهات:');
    debugPrint('   🔴 متأخرة: $overdueCount');
    debugPrint('   🟠 مستحقة اليوم: $dueTodayCount');
    debugPrint('   🔵 مستحقة قريباً: $dueSoonCount');
  }

  // إنشاء تنبيه منفصل لدين واحد
  Future<void> _createIndividualDebtNotification(
    dynamic customer,
    Debt debt,
    NotificationType type,
    NotificationPriority priority,
    String titlePrefix,
    DateTime now,
    int debtNumber,
    int totalDebts,
    NotificationProvider? notificationProvider,
  ) async {
    debugPrint(
        '📝 إنشاء إشعار منفصل للدين: ${debt.itemName} - العميل: ${customer.name}');

    final today = DateTime(now.year, now.month, now.day);
    final dueDate = DateTime(
      debt.dueDate.year,
      debt.dueDate.month,
      debt.dueDate.day,
    );

    // حساب الأيام
    int daysDifference = 0;
    String daysText = '';
    if (type == NotificationType.overdue) {
      daysDifference = today.difference(dueDate).inDays;
      daysText = 'متأخر $daysDifference يوم';
    } else if (type == NotificationType.dueToday) {
      daysText = 'مستحق اليوم';
    } else {
      daysDifference = dueDate.difference(today).inDays;
      daysText = 'مستحق خلال $daysDifference أيام';
    }

    // إنشاء العنوان والرسالة
    String title;
    String message;

    if (totalDebts > 1) {
      title =
          '${customer.name} - دين $debtNumber من $totalDebts ($titlePrefix)';
      message =
          '${debt.itemName} - ${NumberFormatter.formatCurrency(debt.remainingAmount)} - $daysText';
    } else {
      title = '${customer.name} - $titlePrefix';
      message =
          '${debt.itemName} - ${NumberFormatter.formatCurrency(debt.remainingAmount)} - $daysText';
    }

    _addNotification(
      NotificationModel(
        id: '${type.name}_debt_${debt.id}_${customer.id}',
        title: title,
        message: message,
        type: type,
        priority: priority,
        createdAt: now,
        customerId: customer.id?.toString(),
        customerName: customer.name,
        amount: debt.remainingAmount,
        dueDate: debt.dueDate,
        additionalData: {
          'debtId': debt.id,
          'customerName': customer.name,
          'phoneNumber': customer.phoneNumber,
          'amount': debt.remainingAmount,
          'cardType': debt.cardType,
          'itemName': debt.itemName,
          'notes': debt.notes ?? '',
          'entryDate': debt.entryDate.toIso8601String(),
          'dueDate': debt.dueDate.toIso8601String(),
          'daysDifference': daysDifference,
          'debtNumber': debtNumber,
          'totalDebts': totalDebts,
        },
      ),
    );
  }

  // إنشاء تنبيهات المخزون
  Future<void> _generateInventoryNotifications(
    CardInventoryProvider inventoryProvider,
  ) async {
    final now = DateTime.now();

    for (final inventory in inventoryProvider.inventories) {
      // تنبيهات نفاد المخزون
      if (inventory.isOutOfStock) {
        _addNotification(
          NotificationModel(
            id: 'out_of_stock_${inventory.id}',
            title: 'نفاد المخزون',
            message:
                'نفد مخزون ${inventory.cardType} - الكمية: ${inventory.quantity}',
            type: NotificationType.outOfStock,
            priority: NotificationPriority.urgent,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
            },
          ),
        );
      }
      // تنبيهات المخزون المنخفض
      else if (inventory.isLowStock) {
        _addNotification(
          NotificationModel(
            id: 'low_stock_${inventory.id}',
            title: 'مخزون منخفض',
            message:
                'مخزون ${inventory.cardType} منخفض - الكمية: ${inventory.quantity}',
            type: NotificationType.lowStock,
            priority: NotificationPriority.high,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
              'minQuantity': inventory.minQuantity,
            },
          ),
        );
      }
    }
  }

  // إضافة تنبيه مع ضمان عدم التكرار والحفظ الدائم - نظام جوجل محسن
  void _addNotification(NotificationModel notification) {
    // فحص ذكي شامل للتكرار
    if (_isNotificationDuplicate(notification)) {
      debugPrint('⚠️ تم تجاهل تنبيه مكرر: ${notification.title}');
      return;
    }

    // تنبيه جديد - إضافته
    _notifications.add(notification);
    final uniqueId = _generateUniqueNotificationId(notification);

    debugPrint('✅ تم إضافة إشعار جديد: ${notification.title}');
    debugPrint('🆔 المعرف الفريد: $uniqueId');
    debugPrint('📊 إجمالي الإشعارات: ${_notifications.length}');

    // حفظ فوري في التخزين المحلي لضمان البقاء (مثل جوجل)
    _saveNotificationsToStorage();

    notifyListeners();
  }

  // إنشاء معرف فريد للتنبيه لتجنب التكرار
  String _generateUniqueNotificationId(NotificationModel notification) {
    // معرف مركب من: نوع التنبيه + معرف العميل + معرف الدين + التاريخ
    final typePrefix = notification.type.name;
    final customerId = notification.customerId ?? 'unknown';
    final debtId =
        notification.additionalData?['debtId']?.toString() ?? 'no_debt';
    final dateKey =
        notification.createdAt.toIso8601String().substring(0, 10); // YYYY-MM-DD

    return '${typePrefix}_${customerId}_${debtId}_$dateKey';
  }

  // دالة ذكية لمنع التكرار بكل الطرق الممكنة
  bool _isNotificationDuplicate(NotificationModel newNotification) {
    final uniqueId = _generateUniqueNotificationId(newNotification);

    // فحص التكرار بطرق متعددة للتأكد التام
    for (final existing in _notifications) {
      final existingUniqueId = _generateUniqueNotificationId(existing);

      // 1. فحص المعرف الفريد
      if (existingUniqueId == uniqueId) {
        debugPrint('🔍 تكرار بالمعرف الفريد: $uniqueId');
        return true;
      }

      // 2. فحص المعرف العادي
      if (existing.id == newNotification.id) {
        debugPrint('🔍 تكرار بالمعرف العادي: ${newNotification.id}');
        return true;
      }

      // 3. فحص التطابق الكامل للمحتوى
      if (_areNotificationsContentIdentical(existing, newNotification)) {
        debugPrint('🔍 تكرار بالمحتوى: ${newNotification.title}');
        return true;
      }

      // 4. فحص التطابق الذكي (نفس العميل + نفس الدين + نفس النوع + نفس اليوم)
      if (_areNotificationsSmartDuplicate(existing, newNotification)) {
        debugPrint('🔍 تكرار ذكي: ${newNotification.title}');
        return true;
      }
    }

    return false;
  }

  // فحص التطابق الكامل للمحتوى
  bool _areNotificationsContentIdentical(
      NotificationModel a, NotificationModel b) {
    return a.title == b.title &&
        a.message == b.message &&
        a.type == b.type &&
        a.customerId == b.customerId &&
        a.additionalData?['debtId'] == b.additionalData?['debtId'];
  }

  // فحص التطابق الذكي
  bool _areNotificationsSmartDuplicate(
      NotificationModel a, NotificationModel b) {
    // نفس النوع
    if (a.type != b.type) return false;

    // نفس العميل
    if (a.customerId != b.customerId) return false;

    // نفس الدين (إذا وجد)
    final aDebtId = a.additionalData?['debtId'];
    final bDebtId = b.additionalData?['debtId'];
    if (aDebtId != null && bDebtId != null && aDebtId != bDebtId) return false;

    // نفس اليوم
    final aDate =
        DateTime(a.createdAt.year, a.createdAt.month, a.createdAt.day);
    final bDate =
        DateTime(b.createdAt.year, b.createdAt.month, b.createdAt.day);
    if (!aDate.isAtSameMomentAs(bDate)) return false;

    return true;
  }

  // تنظيف التكرارات الموجودة (دالة صيانة)
  void _cleanupDuplicateNotifications() {
    final uniqueNotifications = <NotificationModel>[];
    final seenIds = <String>{};

    for (final notification in _notifications) {
      final uniqueId = _generateUniqueNotificationId(notification);

      if (!seenIds.contains(uniqueId)) {
        seenIds.add(uniqueId);
        uniqueNotifications.add(notification);
      } else {
        debugPrint('🗑️ إزالة تكرار: ${notification.title}');
      }
    }

    final removedCount = _notifications.length - uniqueNotifications.length;
    if (removedCount > 0) {
      _notifications.clear();
      _notifications.addAll(uniqueNotifications);
      debugPrint('✅ تم تنظيف $removedCount تنبيه مكرر');
      _saveNotificationsToStorage();
      notifyListeners();
    }
  }

  // دالة عامة لتنظيف التكرارات (يمكن استدعاؤها من الخارج)
  void cleanupDuplicates() {
    debugPrint('🧹 بدء تنظيف التكرارات الذكي...');
    _cleanupDuplicateNotifications();
  }

  // فحص وجود تكرارات
  int getDuplicatesCount() {
    final seenIds = <String>{};
    int duplicates = 0;

    for (final notification in _notifications) {
      final uniqueId = _generateUniqueNotificationId(notification);
      if (seenIds.contains(uniqueId)) {
        duplicates++;
      } else {
        seenIds.add(uniqueId);
      }
    }

    return duplicates;
  }

  // إضافة تنبيه من خدمة خارجية
  void addExternalNotification(NotificationModel notification) {
    _addNotification(notification);
  }

  // ترتيب التنبيهات حسب نظام جوجل (الأولوية + القراءة + التاريخ)
  void _sortNotifications() {
    _notifications.sort((a, b) {
      // 1. غير المقروءة أولاً (مثل جوجل)
      if (a.isRead != b.isRead) {
        return a.isRead ? 1 : -1;
      }

      // 2. ترتيب حسب الأولوية للتنبيهات غير المقروءة
      if (!a.isRead && !b.isRead) {
        final priorityComparison = _getPriorityValue(b.priority)
            .compareTo(_getPriorityValue(a.priority));
        if (priorityComparison != 0) return priorityComparison;
      }

      // 3. أخيراً حسب التاريخ (الأحدث أولاً مثل جوجل)
      return b.createdAt.compareTo(a.createdAt);
    });

    debugPrint('🔄 تم ترتيب ${_notifications.length} تنبيه حسب نظام جوجل');
  }

  // الحصول على قيمة الأولوية للترتيب
  int _getPriorityValue(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return 4;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.medium:
        return 2;
      case NotificationPriority.low:
        return 1;
    }
  }

  // تحديد التنبيه كمقروء بدون إزالته - نظام جوجل (البطاقات تبقى ظاهرة دائماً)
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      // تحديد الإشعار كمقروء فقط بدون إزالته (مثل جوجل)
      _notifications[index] = _notifications[index].copyWith(isRead: true);

      // إعادة ترتيب التنبيهات (المقروءة تنتقل للأسفل مثل جوجل)
      _sortNotifications();

      notifyListeners();

      // حفظ فوري في التخزين المحلي (مثل جوجل)
      _saveNotificationsToStorage();

      // إزالة الإشعار المحلي المقابل إذا وجد
      _removeLocalNotificationIfExists(notificationId);

      debugPrint(
          '✅ تم تحديد الإشعار $notificationId كمقروء - البطاقة تبقى ظاهرة (نظام جوجل)');
    }
  }

  // تحديد جميع التنبيهات كمقروءة بدون إزالتها - البطاقات تبقى ظاهرة دائماً
  void markAllAsRead() {
    // تحديد جميع الإشعارات كمقروءة فقط بدون إزالتها
    final updatedCount = _notifications.length;
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    notifyListeners();

    // حفظ التحديث في التخزين المحلي
    _saveNotificationsToStorage();

    // إزالة جميع الإشعارات المحلية
    _removeAllLocalNotifications();

    debugPrint(
        '✅ تم تحديد جميع الإشعارات ($updatedCount) كمقروءة - البطاقات تبقى ظاهرة');
  }

  // إزالة الإشعار المحلي إذا وجد
  void _removeLocalNotificationIfExists(String notificationId) {
    try {
      // محاولة تحويل معرف الإشعار إلى رقم
      // معرف الإشعار قد يكون في شكل: "notification_123" أو "123"
      int? localNotificationId;

      if (notificationId.contains('_')) {
        final parts = notificationId.split('_');
        if (parts.length > 1) {
          localNotificationId = int.tryParse(parts.last);
        }
      } else {
        localNotificationId = int.tryParse(notificationId);
      }

      if (localNotificationId != null) {
        LocalNotificationService().cancelNotification(localNotificationId);
        debugPrint('✅ تم إزالة الإشعار المحلي: $localNotificationId');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الإشعار المحلي: $e');
    }
  }

  // إزالة جميع الإشعارات المحلية
  void _removeAllLocalNotifications() {
    try {
      LocalNotificationService().cancelAllNotifications();
      debugPrint('✅ تم إزالة جميع الإشعارات المحلية');
    } catch (e) {
      debugPrint('خطأ في إزالة الإشعارات المحلية: $e');
    }
  }

  // تحديث إشعار الملخص في إشعارات الموبايل
  Future<void> updateNotificationSummary() async {
    try {
      final localNotificationService = LocalNotificationService();

      if (_notifications.isEmpty) {
        // إزالة إشعار الملخص إذا لم تعد هناك إشعارات
        await localNotificationService.cancelSummaryNotification();
        return;
      }

      final totalCount = _notifications.length;
      final unreadCount = this.unreadCount;
      final recentTitles = _notifications
          .take(5)
          .map((n) => '${n.type.icon} ${n.title}')
          .toList();

      // تجميع البيانات حسب نوع الإشعار
      final overdueNotifications = _notifications
          .where((n) => n.type == NotificationType.overdue)
          .toList();
      final dueTodayNotifications = _notifications
          .where((n) => n.type == NotificationType.dueToday)
          .toList();

      // استخراج تفاصيل العملاء والمبالغ وأرقام الهواتف
      final overdueCustomers = <Map<String, dynamic>>[];
      final dueTodayCustomers = <Map<String, dynamic>>[];
      double totalOverdueAmount = 0;
      double totalDueTodayAmount = 0;

      for (final notification in overdueNotifications) {
        // استخراج تفاصيل العميل من البيانات الإضافية
        final customerData = _extractCustomerDetails(notification);
        if (customerData['name'].isNotEmpty) {
          overdueCustomers.add(customerData);
          totalOverdueAmount += customerData['amount'];
        }
      }

      for (final notification in dueTodayNotifications) {
        final customerData = _extractCustomerDetails(notification);
        if (customerData['name'].isNotEmpty) {
          dueTodayCustomers.add(customerData);
          totalDueTodayAmount += customerData['amount'];
        }
      }

      // عرض أو تحديث إشعار الملخص
      await localNotificationService.showNotificationSummary(
        totalCount: totalCount,
        unreadCount: unreadCount,
        recentTitles: recentTitles,
        overdueCustomers: overdueCustomers,
        dueTodayCustomers: dueTodayCustomers,
        totalOverdueAmount: totalOverdueAmount,
        totalDueTodayAmount: totalDueTodayAmount,
      );

      debugPrint(
        '✅ تم تحديث إشعار الملخص: $totalCount إجمالي، $unreadCount غير مقروء',
      );
    } catch (e) {
      debugPrint('خطأ في تحديث إشعار الملخص: $e');
    }
  }

  // استخراج تفاصيل العميل الكاملة من الإشعار
  Map<String, dynamic> _extractCustomerDetails(NotificationModel notification) {
    final additionalData = notification.additionalData ?? {};

    // استخراج اسم العميل من العنوان أو البيانات الإضافية
    String customerName = '';
    if (additionalData.containsKey('customerName')) {
      customerName = additionalData['customerName'].toString();
    } else {
      // البحث عن اسم العميل في العنوان
      final regex = RegExp(r'العميل (.+?) لديه');
      final match = regex.firstMatch(notification.title);
      customerName = match?.group(1) ?? '';
    }

    // استخراج رقم الهاتف
    final phoneNumber = additionalData['phoneNumber']?.toString() ??
        additionalData['phone']?.toString() ??
        '';

    // استخراج المبلغ
    final amount = _extractAmount(additionalData);

    // استخراج عدد الديون
    final debtCount = additionalData['debtCount'] ?? 1;

    // استخراج تاريخ الاستحقاق
    final dueDate = additionalData['dueDate']?.toString() ?? '';

    // استخراج نوع البطاقة أو تفاصيل الدين
    final cardTypes = additionalData['cardTypes'] as List<dynamic>? ?? [];
    final cardTypesText = cardTypes.isNotEmpty
        ? cardTypes.take(2).join('، ') + (cardTypes.length > 2 ? '...' : '')
        : '';

    return {
      'name': customerName,
      'phone': phoneNumber,
      'amount': amount,
      'debtCount': debtCount,
      'dueDate': dueDate,
      'cardTypes': cardTypesText,
      'formattedAmount': _formatAmountForNotification(amount),
    };
  }

  // استخراج المبلغ من البيانات الإضافية
  double _extractAmount(Map<String, dynamic>? additionalData) {
    if (additionalData == null) return 0;

    // البحث عن المبلغ في البيانات الإضافية
    final amount = additionalData['totalAmount'] ??
        additionalData['amount'] ??
        additionalData['remainingAmount'] ??
        0;

    return (amount is num) ? amount.toDouble() : 0;
  }

  // تنسيق المبلغ للإشعارات
  String _formatAmountForNotification(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }

  // حذف تنبيه
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  // إزالة التنبيهات المرتبطة بدين معين
  void removeNotificationsByDebt(int debtId) {
    final removedCount = _notifications.length;
    _notifications.removeWhere((n) =>
        n.additionalData?['debtId'] == debtId ||
        n.id.contains('debt_$debtId') ||
        n.id.contains('overdue_$debtId') ||
        n.id.contains('due_today_$debtId') ||
        n.id.contains('due_soon_$debtId'));
    final newCount = _notifications.length;
    if (removedCount != newCount) {
      debugPrint('🗑️ تم إزالة ${removedCount - newCount} تنبيه للدين $debtId');
      notifyListeners();

      // حفظ التحديث في التخزين المحلي
      _saveNotificationsToStorage();
    }
  }

  // إضافة تنبيه لدين معين
  void addDebtNotification(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate =
        DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);

    NotificationType type;
    NotificationPriority priority;
    String title;
    String message;

    if (dueDate.isBefore(today)) {
      type = NotificationType.overdue;
      priority = NotificationPriority.urgent;
      title = 'دين متأخر';
      message =
          'دين ${debt.itemName} متأخر منذ ${today.difference(dueDate).inDays} يوم';
    } else if (dueDate.isAtSameMomentAs(today)) {
      type = NotificationType.dueToday;
      priority = NotificationPriority.high;
      title = 'دين مستحق اليوم';
      message = 'دين ${debt.itemName} مستحق اليوم';
    } else {
      type = NotificationType.dueSoon;
      priority = NotificationPriority.medium;
      title = 'دين مستحق قريباً';
      message =
          'دين ${debt.itemName} مستحق خلال ${dueDate.difference(today).inDays} يوم';
    }

    final notification = NotificationModel(
      id: '${type.name}_${debt.id}_${now.millisecondsSinceEpoch % 2147483647}', // تقليل الرقم ليكون ضمن حدود 32-bit integer
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: now,
      additionalData: {
        'debtId': debt.id,
        'customerId': debt.customerId,
        'amount': debt.remainingAmount,
        'cardType': debt.cardType,
        'itemName': debt.itemName,
      },
    );

    _addNotification(notification);
  }

  // مسح التنبيهات مع الاحتفاظ بالمهمة
  void clearAllNotifications({bool keepImportant = true}) {
    if (keepImportant) {
      // الاحتفاظ بالتنبيهات المهمة والمقروءة
      final importantNotifications = _notifications
          .where((n) => n.isRead || n.priority == NotificationPriority.urgent)
          .toList();

      _notifications.clear();
      _notifications.addAll(importantNotifications);

      debugPrint(
          '🔄 تم مسح التنبيهات مع الاحتفاظ بـ ${importantNotifications.length} تنبيه مهم');
    } else {
      // مسح جميع التنبيهات
      _notifications.clear();
      debugPrint('🗑️ تم مسح جميع التنبيهات');
    }

    notifyListeners();

    // حفظ التحديث في التخزين المحلي
    _saveNotificationsToStorage();
  }

  // مسح التنبيهات القديمة فقط
  void clearOldNotifications({int maxDays = 30}) {
    final now = DateTime.now();
    final oldCount = _notifications.length;

    _notifications.removeWhere((notification) {
      // عدم مسح التنبيهات المقروءة أو المهمة
      if (notification.isRead ||
          notification.priority == NotificationPriority.urgent) {
        return false;
      }

      // مسح التنبيهات القديمة
      final daysSinceCreated = now.difference(notification.createdAt).inDays;
      return daysSinceCreated > maxDays;
    });

    final removedCount = oldCount - _notifications.length;
    if (removedCount > 0) {
      debugPrint('🗑️ تم مسح $removedCount تنبيه قديم');
      notifyListeners();
      _saveNotificationsToStorage();
    }
  }

  // تشغيل الإشعارات التلقائية (يتم استدعاؤها دورياً)
  Future<void> triggerAutomaticNotifications({
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardTypeProvider cardTypeProvider,
    NotificationProvider? notificationProvider,
  }) async {
    // إزالة قيود الإعدادات - التنبيهات تعمل دائماً
    // if (notificationProvider == null ||
    //     !notificationProvider.settings.localNotificationsEnabled ||
    //     !notificationProvider.settings.debtNotificationsEnabled) {
    //   return;
    // }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // البحث عن الديون المتأخرة
    final overdueDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isBefore(today); // && debt.remainingAmount > 0;
    }).toList();

    // البحث عن الديون المستحقة اليوم
    final dueTodayDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isAtSameMomentAs(today); // && debt.remainingAmount > 0;
    }).toList();

    // البحث عن الديون المستحقة قريباً
    final dueSoonDays = notificationProvider?.settings.dueSoonDays ?? 3;
    final dueSoonDate = today.add(Duration(days: dueSoonDays));
    final dueSoonDebts = debtProvider.debts.where((debt) {
      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      return dueDate.isAfter(today) &&
          dueDate.isBefore(dueSoonDate.add(const Duration(days: 1)));
      // && debt.remainingAmount > 0;
    }).toList();

    final localNotificationService = LocalNotificationService();

    // إرسال إشعارات الديون المتأخرة - إشعار منفصل لكل دين
    if (overdueDebts.isNotEmpty) {
      // && notificationProvider?.settings.overdueNotificationsEnabled == true) {
      for (final debt in overdueDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين متأخر',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // إرسال إشعار منفصل لكل دين متأخر
        await localNotificationService.showOverdueDebtNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين متأخر منفصل: ${customer.name} - ${debt.remainingAmount}',
        );
      }
    }

    // إرسال إشعارات الديون المستحقة اليوم - إشعار منفصل لكل دين
    if (dueTodayDebts.isNotEmpty) {
      // && notificationProvider?.settings.dueTodayNotificationsEnabled == true) {
      for (final debt in dueTodayDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين مستحق اليوم',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // إرسال إشعار منفصل لكل دين مستحق اليوم
        await localNotificationService.showDueTodayNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين مستحق اليوم منفصل: ${customer.name} - ${debt.remainingAmount}',
        );
      }
    }

    // إرسال إشعارات الديون المستحقة قريباً - إشعار منفصل لكل دين
    if (dueSoonDebts.isNotEmpty) {
      // && notificationProvider?.settings.dueSoonNotificationsEnabled == true) {
      for (final debt in dueSoonDebts) {
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => throw Exception('Customer not found'),
        );

        // تحديث رقم هاتف العميل في LocalNotificationService
        if (customer.phone != null && customer.phone!.isNotEmpty) {
          localNotificationService.addCustomerPhone(
            customer.id?.toString() ?? '',
            customer.phone!,
          );
        }

        // تحديث معلومات البطاقة الحقيقية مع الاسم الحقيقي للكارت
        final cardInfo = {
          'cardType': _getRealCardTypeName(debt.cardType, cardTypeProvider),
          'quantity': debt.quantity.toString(),
          'notes': debt.notes ?? 'دين مستحق قريباً',
          'entryDate': _formatDateWithDayAndTime(debt.entryDate),
          'dueDate': _formatDateWithDay(debt.dueDate),
        };
        localNotificationService.addCardInfo(
          customer.id?.toString() ?? '',
          cardInfo,
        );

        // حساب عدد الأيام حتى الاستحقاق
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );
        final daysUntilDue = dueDate.difference(today).inDays;

        // إرسال إشعار منفصل لكل دين مستحق قريباً
        await localNotificationService.showDueSoonNotification(
          customerName: customer.name,
          amount: debt.remainingAmount,
          debtCount: 1, // دين واحد فقط
          daysUntilDue: daysUntilDue,
          customerId: customer.id?.toString(),
        );

        debugPrint(
          '📱 تم إرسال إشعار دين مستحق قريباً منفصل: ${customer.name} - ${debt.remainingAmount} - $daysUntilDue أيام',
        );
      }
    }

    debugPrint('✅ تم تشغيل الإشعارات التلقائية');
  }

  // الحصول على الاسم الحقيقي للكارت
  String _getRealCardTypeName(
    String? cardType,
    CardTypeProvider cardTypeProvider,
  ) {
    if (cardType == null || cardType.isEmpty) return 'غير محدد';

    debugPrint('🔍 معالجة نوع الكارت: $cardType');

    // إذا كان نوع مخصص، احصل على الاسم الفعلي
    if (cardType.startsWith('custom_')) {
      try {
        final cardTypeId = int.parse(cardType.replaceFirst('custom_', ''));
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );
        debugPrint('✅ تم العثور على كارت مخصص: ${customCardType.displayName}');
        return customCardType.displayName; // الاسم الحقيقي المخصص
      } catch (e) {
        debugPrint('❌ خطأ في الحصول على نوع الكارت المخصص: $e');
        return 'كارت مخصص';
      }
    } else {
      // ترجمة الأسماء الإنجليزية إلى العربية
      final Map<String, String> translations = {
        'cash': 'نقدي',
        'visa': 'فيزا',
        'mastercard': 'ماستركارد',
        'mada': 'مدى',
        'americanExpress': 'أمريكان إكسبريس',
        'american_express': 'أمريكان إكسبريس',
        'zain': 'زين',
        'sia': 'آسيا',
        'abuAshara': 'أبو العشرة',
        'abu_ashara': 'أبو العشرة',
        'abuSitta': 'أبو الستة',
        'abu_sitta': 'أبو الستة',
        'other': 'أخرى',
        'stc_pay': 'STC Pay',
        'apple_pay': 'Apple Pay',
        'samsung_pay': 'Samsung Pay',
        'paypal': 'PayPal',
        'bank_transfer': 'تحويل بنكي',
        'check': 'شيك',
        'credit': 'ائتمان',
        'debit': 'خصم مباشر',
        'gift_card': 'بطاقة هدية',
        'loyalty_points': 'نقاط الولاء',
      };

      // البحث عن الترجمة المباشرة
      if (translations.containsKey(cardType.toLowerCase())) {
        final translatedName = translations[cardType.toLowerCase()]!;
        debugPrint('✅ تم ترجمة الكارت: $cardType -> $translatedName');
        return translatedName;
      }

      // محاولة البحث في enum الافتراضي
      try {
        final cardTypeEnum = CardType.values.firstWhere(
          (ct) => ct.name.toLowerCase() == cardType.toLowerCase(),
          orElse: () => CardType.other,
        );
        debugPrint('✅ تم العثور على كارت افتراضي: ${cardTypeEnum.displayName}');
        return cardTypeEnum.displayName;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على ترجمة للكارت: $cardType');
        // إذا لم نجد ترجمة، نعيد النص الأصلي
        return cardType;
      }
    }
  }

  // تنسيق التاريخ مع اسم اليوم والوقت
  String _formatDateWithDayAndTime(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    // تحديد صباح أم مساء
    final hour = date.hour;
    final minute = date.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '$dayName ${date.day} $monthName ${date.year} - $displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  // تنسيق التاريخ مع اسم اليوم فقط (للاستحقاق)
  String _formatDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    return '$dayName ${date.day} $monthName ${date.year}';
  }

  // الحصول على إحصائيات التنبيهات
  Map<String, int> getNotificationStats() {
    return {
      'total': _notifications.length,
      'unread': unreadCount,
      'overdue': getNotificationsByType(NotificationType.overdue).length,
      'dueToday': getNotificationsByType(NotificationType.dueToday).length,
      'dueSoon': getNotificationsByType(NotificationType.dueSoon).length,
      'lowStock': getNotificationsByType(NotificationType.lowStock).length,
      'outOfStock': getNotificationsByType(NotificationType.outOfStock).length,
    };
  }

  // تهيئة التنبيهات من التخزين المحلي مع ضمان البقاء
  Future<void> initializeNotifications() async {
    if (_isInitialized) return;

    debugPrint('🔄 تهيئة التنبيهات الذكية من التخزين المحلي...');

    try {
      await _loadNotificationsFromStorage();

      // التأكد من أن التنبيهات المهمة لا تزال صالحة
      final validNotifications = <NotificationModel>[];
      final now = DateTime.now();

      for (final notification in _notifications) {
        // الاحتفاظ بجميع التنبيهات المقروءة
        if (notification.isRead) {
          validNotifications.add(notification);
          continue;
        }

        // الاحتفاظ بالتنبيهات المهمة حتى لو كانت قديمة
        if (notification.priority == NotificationPriority.urgent) {
          validNotifications.add(notification);
          continue;
        }

        // الاحتفاظ بالتنبيهات الحديثة (أقل من 30 يوم)
        final daysSinceCreated = now.difference(notification.createdAt).inDays;
        if (daysSinceCreated <= 30) {
          validNotifications.add(notification);
        } else {
          debugPrint('🗑️ إزالة تنبيه قديم: ${notification.title}');
        }
      }

      _notifications.clear();
      _notifications.addAll(validNotifications);

      debugPrint('✅ تم تهيئة ${_notifications.length} تنبيه من التخزين المحلي');
      debugPrint('🔔 غير مقروء: $unreadCount');

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التنبيهات: $e');
      _isInitialized = true; // تجنب المحاولة مرة أخرى
    }
  }

  // حفظ التنبيهات في التخزين المحلي
  Future<void> _saveNotificationsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = _notifications.map((n) => n.toJson()).toList();
      await prefs.setString(
          'smart_notifications', json.encode(notificationsJson));
      debugPrint('💾 تم حفظ ${_notifications.length} تنبيه في التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ التنبيهات: $e');
    }
  }

  // تحميل التنبيهات من التخزين المحلي
  Future<void> _loadNotificationsFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsString = prefs.getString('smart_notifications');

      if (notificationsString != null) {
        final List<dynamic> notificationsJson =
            json.decode(notificationsString);
        _notifications.clear();

        for (final notificationData in notificationsJson) {
          try {
            final notification = NotificationModel.fromJson(notificationData);
            _notifications.add(notification);
          } catch (e) {
            debugPrint('⚠️ خطأ في تحليل تنبيه: $e');
          }
        }

        debugPrint(
            '📥 تم تحميل ${_notifications.length} تنبيه من التخزين المحلي');
        notifyListeners();
      } else {
        debugPrint('📭 لا توجد تنبيهات محفوظة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل التنبيهات: $e');
    }
  }

  // مسح التنبيهات من التخزين المحلي
  Future<void> clearStoredNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('smart_notifications');
      debugPrint('🗑️ تم مسح التنبيهات من التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في مسح التنبيهات: $e');
    }
  }
}
