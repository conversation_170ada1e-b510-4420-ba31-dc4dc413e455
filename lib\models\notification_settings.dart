class NotificationSettings {
  // Constructor
  NotificationSettings({
    this.salesNotificationsEnabled = true,
    this.inventoryNotificationsEnabled = true,
    this.notificationDuration = 3,
    this.lowStockThreshold = 10,
    this.dailyInventoryReminder = true,
    this.reminderTime = '09:00',
    // إعدادات الإشعارات المحلية الجديدة
    this.localNotificationsEnabled = true,
    this.debtNotificationsEnabled = true,
    this.overdueNotificationsEnabled = true,
    this.dueTodayNotificationsEnabled = true,
    this.dueSoonNotificationsEnabled = true,
    this.notificationSoundEnabled = true,
    this.notificationVibrationEnabled = true,
    this.dueSoonDays = 3,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      salesNotificationsEnabled: json['salesNotificationsEnabled'] ?? true,
      inventoryNotificationsEnabled:
          json['inventoryNotificationsEnabled'] ?? true,
      notificationDuration: json['notificationDuration'] ?? 3,
      lowStockThreshold: json['lowStockThreshold'] ?? 10,
      dailyInventoryReminder: json['dailyInventoryReminder'] ?? true,
      reminderTime: json['reminderTime'] ?? '09:00',
      // إعدادات الإشعارات المحلية الجديدة
      localNotificationsEnabled: json['localNotificationsEnabled'] ?? true,
      debtNotificationsEnabled: json['debtNotificationsEnabled'] ?? true,
      overdueNotificationsEnabled: json['overdueNotificationsEnabled'] ?? true,
      dueTodayNotificationsEnabled:
          json['dueTodayNotificationsEnabled'] ?? true,
      dueSoonNotificationsEnabled: json['dueSoonNotificationsEnabled'] ?? true,
      notificationSoundEnabled: json['notificationSoundEnabled'] ?? true,
      notificationVibrationEnabled:
          json['notificationVibrationEnabled'] ?? true,
      dueSoonDays: json['dueSoonDays'] ?? 3,
    );
  }

  // Fields
  final bool salesNotificationsEnabled;
  final bool inventoryNotificationsEnabled;
  final int notificationDuration; // in seconds
  final int lowStockThreshold; // minimum quantity before alert
  final bool dailyInventoryReminder;
  final String reminderTime; // HH:mm format

  // إعدادات الإشعارات المحلية الجديدة
  final bool localNotificationsEnabled; // تفعيل الإشعارات المحلية
  final bool debtNotificationsEnabled; // تفعيل إشعارات الديون
  final bool overdueNotificationsEnabled; // تفعيل إشعارات الديون المتأخرة
  final bool
  dueTodayNotificationsEnabled; // تفعيل إشعارات الديون المستحقة اليوم
  final bool
  dueSoonNotificationsEnabled; // تفعيل إشعارات الديون المستحقة قريباً
  final bool notificationSoundEnabled; // تفعيل أصوات الإشعارات
  final bool notificationVibrationEnabled; // تفعيل اهتزاز الإشعارات
  final int dueSoonDays; // عدد الأيام للديون المستحقة قريباً

  Map<String, dynamic> toJson() {
    return {
      'salesNotificationsEnabled': salesNotificationsEnabled,
      'inventoryNotificationsEnabled': inventoryNotificationsEnabled,
      'notificationDuration': notificationDuration,
      'lowStockThreshold': lowStockThreshold,
      'dailyInventoryReminder': dailyInventoryReminder,
      'reminderTime': reminderTime,
      // إعدادات الإشعارات المحلية الجديدة
      'localNotificationsEnabled': localNotificationsEnabled,
      'debtNotificationsEnabled': debtNotificationsEnabled,
      'overdueNotificationsEnabled': overdueNotificationsEnabled,
      'dueTodayNotificationsEnabled': dueTodayNotificationsEnabled,
      'dueSoonNotificationsEnabled': dueSoonNotificationsEnabled,
      'notificationSoundEnabled': notificationSoundEnabled,
      'notificationVibrationEnabled': notificationVibrationEnabled,
      'dueSoonDays': dueSoonDays,
    };
  }

  NotificationSettings copyWith({
    bool? salesNotificationsEnabled,
    bool? inventoryNotificationsEnabled,
    int? notificationDuration,
    int? lowStockThreshold,
    bool? dailyInventoryReminder,
    String? reminderTime,
    // إعدادات الإشعارات المحلية الجديدة
    bool? localNotificationsEnabled,
    bool? debtNotificationsEnabled,
    bool? overdueNotificationsEnabled,
    bool? dueTodayNotificationsEnabled,
    bool? dueSoonNotificationsEnabled,
    bool? notificationSoundEnabled,
    bool? notificationVibrationEnabled,
    int? dueSoonDays,
  }) {
    return NotificationSettings(
      salesNotificationsEnabled:
          salesNotificationsEnabled ?? this.salesNotificationsEnabled,
      inventoryNotificationsEnabled:
          inventoryNotificationsEnabled ?? this.inventoryNotificationsEnabled,
      notificationDuration: notificationDuration ?? this.notificationDuration,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      dailyInventoryReminder:
          dailyInventoryReminder ?? this.dailyInventoryReminder,
      reminderTime: reminderTime ?? this.reminderTime,
      // إعدادات الإشعارات المحلية الجديدة
      localNotificationsEnabled:
          localNotificationsEnabled ?? this.localNotificationsEnabled,
      debtNotificationsEnabled:
          debtNotificationsEnabled ?? this.debtNotificationsEnabled,
      overdueNotificationsEnabled:
          overdueNotificationsEnabled ?? this.overdueNotificationsEnabled,
      dueTodayNotificationsEnabled:
          dueTodayNotificationsEnabled ?? this.dueTodayNotificationsEnabled,
      dueSoonNotificationsEnabled:
          dueSoonNotificationsEnabled ?? this.dueSoonNotificationsEnabled,
      notificationSoundEnabled:
          notificationSoundEnabled ?? this.notificationSoundEnabled,
      notificationVibrationEnabled:
          notificationVibrationEnabled ?? this.notificationVibrationEnabled,
      dueSoonDays: dueSoonDays ?? this.dueSoonDays,
    );
  }
}
