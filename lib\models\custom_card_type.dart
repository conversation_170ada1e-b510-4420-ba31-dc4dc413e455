import 'package:flutter/material.dart';

class CustomCardType {
  // Constructor
  CustomCardType({
    this.id,
    required this.name,
    required this.displayName,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى Object
  factory CustomCardType.fromMap(Map<String, dynamic> map) {
    return CustomCardType(
      id: map['id'],
      name: map['name'],
      displayName: map['display_name'],
      createdAt: _parseDateTime(map['created_at']),
      updatedAt: _parseDateTime(map['updated_at']),
    );
  }

  // Fields
  final int? id;
  final String name;
  final String displayName;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Helper method to parse DateTime from both ISO string and milliseconds
  static DateTime _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return DateTime.now();

    if (dateTime is String) {
      try {
        return DateTime.parse(dateTime);
      } catch (e) {
        return DateTime.now();
      }
    }

    if (dateTime is int) {
      return DateTime.fromMillisecondsSinceEpoch(dateTime);
    }

    return DateTime.now(); // Default fallback
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'display_name': displayName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة
  CustomCardType copyWith({
    int? id,
    String? name,
    String? displayName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomCardType(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomCardType &&
        other.id == id &&
        other.name == name &&
        other.displayName == displayName &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        displayName.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  @override
  String toString() {
    return 'CustomCardType(id: $id, name: $name, displayName: $displayName, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

// فئة موحدة لأنواع الكروت (الافتراضية والمخصصة)
class CardTypeOption {
  CardTypeOption({
    required this.id,
    required this.displayName,
    required this.isCustom,
    this.defaultType,
    this.customType,
  });

  factory CardTypeOption.fromDefault(CardType cardType) {
    return CardTypeOption(
      id: cardType.name,
      displayName:
          cardType.displayName, // هذا يستخدم الأسماء العربية من extension
      isCustom: false,
      defaultType: cardType,
    );
  }

  factory CardTypeOption.fromCustom(CustomCardType customType) {
    return CardTypeOption(
      id: 'custom_${customType.id}',
      displayName: customType.displayName,
      isCustom: true,
      customType: customType,
    );
  }

  final String id;
  final String displayName;
  final bool isCustom;
  final CardType? defaultType;
  final CustomCardType? customType;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CardTypeOption && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CardTypeOption(id: $id, displayName: $displayName, isCustom: $isCustom)';
  }
}

// إضافة enum للكروت الافتراضية
enum CardType {
  cash,
  visa,
  mastercard,
  americanExpress,
  zain,
  sia,
  abuAshara,
  abuSitta,
  other,
}

extension CardTypeExtension on CardType {
  String get displayName {
    switch (this) {
      case CardType.cash:
        return 'نقدي';
      case CardType.visa:
        return 'فيزا';
      case CardType.mastercard:
        return 'ماستركارد';
      case CardType.americanExpress:
        return 'أمريكان إكسبريس';
      case CardType.zain:
        return 'زين';
      case CardType.sia:
        return 'آسيا';
      case CardType.abuAshara:
        return 'أبو العشرة';
      case CardType.abuSitta:
        return 'أبو الستة';
      case CardType.other:
        return 'أخرى';
    }
  }

  // إضافة دالة للحصول على اللون المحدد لكل نوع كارت
  Color get color {
    switch (this) {
      case CardType.cash:
        return Colors.green;
      case CardType.visa:
        return Colors.blue;
      case CardType.mastercard:
        return Colors.red;
      case CardType.americanExpress:
        return Colors.purple;
      case CardType.zain:
        return Colors.purple; // بنفسجي - فئة 5 آلاف
      case CardType.sia:
        return Colors.red; // أحمر - فئة 5 آلاف
      case CardType.abuAshara:
        return Colors.cyan.shade300; // فايروسي فاتح - فئة 10 آلاف
      case CardType.abuSitta:
        return Colors.grey.shade300; // أبيض - فئة 6 آلاف
      case CardType.other:
        return Colors.orange;
    }
  }

  // إضافة دالة لتحديد نوع الكارت من رقم البطاقة
  static CardType getCardTypeFromNumber(String cardNumber) {
    if (cardNumber.isEmpty) return CardType.cash;

    // إزالة المسافات والرموز
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanNumber.isEmpty) return CardType.cash;

    // تحديد نوع الكارت بناءً على الرقم
    if (cleanNumber.startsWith('4')) {
      return CardType.visa;
    } else if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) {
      return CardType.mastercard;
    } else if (cleanNumber.startsWith('3')) {
      return CardType.americanExpress;
    } else if (cleanNumber.startsWith('6')) {
      return CardType.abuSitta; // أبو الستة
    } else if (cleanNumber.startsWith('10') || cleanNumber.contains('10')) {
      return CardType.abuAshara; // أبو العشرة
    } else if (cleanNumber.contains('زين') || cleanNumber.contains('zain')) {
      return CardType.zain;
    } else if (cleanNumber.contains('سيا') || cleanNumber.contains('sia')) {
      return CardType.sia;
    }

    return CardType.other;
  }
}
