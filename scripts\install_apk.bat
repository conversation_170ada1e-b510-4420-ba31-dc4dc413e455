@echo off
echo ========================================
echo    APK Installer
echo    Professional Debt Accountant
echo ========================================
echo.

REM Check if ADB is available
where adb >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo [WARNING] ADB not found in PATH
    echo Please install Android SDK Platform Tools
    echo.
    echo You can still install manually:
    echo 1. Copy APK to your device
    echo 2. Enable "Unknown Sources" in settings
    echo 3. Tap APK file to install
    echo.
    pause
    exit /b 1
)

echo [OK] ADB found
echo.

REM Check for APK file
if not exist "debt_accountant_pro.apk" (
    echo [ERROR] APK file not found: debt_accountant_pro.apk
    echo Please build APK first using start_auto_build.bat
    echo.
    pause
    exit /b 1
)

echo [OK] APK file found
echo.

REM Show APK info
for %%A in ("debt_accountant_pro.apk") do (
    set /a size_mb=%%~zA/1024/1024
    echo APK Information:
    echo   File: debt_accountant_pro.apk
    echo   Size: %%~zA bytes
    echo   Modified: %%~tA
)
echo.

REM Check connected devices
echo Checking connected devices...
adb devices
echo.

REM Count devices
for /f "skip=1 tokens=1" %%i in ('adb devices') do (
    if not "%%i"=="List" (
        set device_found=1
        goto device_check_done
    )
)
set device_found=0

:device_check_done
if %device_found%==0 (
    echo [ERROR] No Android devices found
    echo.
    echo Please:
    echo 1. Connect your Android device via USB
    echo 2. Enable USB Debugging in Developer Options
    echo 3. Allow USB Debugging when prompted
    echo.
    echo Or install manually:
    echo 1. Copy debt_accountant_pro.apk to your device
    echo 2. Enable "Unknown Sources" in settings
    echo 3. Tap APK file to install
    echo.
    pause
    exit /b 1
)

echo [OK] Android device(s) connected
echo.

echo Installation Options:
echo =====================
echo [1] Install APK (new installation)
echo [2] Update APK (if already installed)
echo [3] Uninstall existing app first, then install
echo [4] Install to specific device
echo [0] Cancel
echo.

set /p choice="Select option (0-4): "

if "%choice%"=="1" goto install_new
if "%choice%"=="2" goto install_update
if "%choice%"=="3" goto uninstall_install
if "%choice%"=="4" goto install_specific
if "%choice%"=="0" goto cancel
goto invalid

:install_new
echo.
echo Installing APK...
echo =================
adb install debt_accountant_pro.apk
if %ERRORLEVEL% equ 0 (
    echo [SUCCESS] APK installed successfully!
) else (
    echo [ERROR] Installation failed
    echo Try using "Update APK" option if app is already installed
)
goto end

:install_update
echo.
echo Updating APK...
echo ===============
adb install -r debt_accountant_pro.apk
if %ERRORLEVEL% equ 0 (
    echo [SUCCESS] APK updated successfully!
) else (
    echo [ERROR] Update failed
)
goto end

:uninstall_install
echo.
echo Uninstalling existing app...
echo ============================
adb uninstall com.example.mahasb
echo.
echo Installing new APK...
echo =====================
adb install debt_accountant_pro.apk
if %ERRORLEVEL% equ 0 (
    echo [SUCCESS] APK installed successfully!
) else (
    echo [ERROR] Installation failed
)
goto end

:install_specific
echo.
echo Available devices:
adb devices
echo.
set /p device_id="Enter device ID: "
echo.
echo Installing to device: %device_id%
echo ==================================
adb -s %device_id% install debt_accountant_pro.apk
if %ERRORLEVEL% equ 0 (
    echo [SUCCESS] APK installed successfully on %device_id%!
) else (
    echo [ERROR] Installation failed on %device_id%
)
goto end

:invalid
echo.
echo [ERROR] Invalid option
goto end

:cancel
echo.
echo Installation cancelled
goto end

:end
echo.
echo ========================================
if %ERRORLEVEL% equ 0 (
    echo Installation completed!
    echo.
    echo The app should now be available on your device.
    echo Look for "Professional Debt Accountant" in your app drawer.
) else (
    echo Installation had issues.
    echo.
    echo Manual installation steps:
    echo 1. Copy debt_accountant_pro.apk to your device
    echo 2. Enable "Unknown Sources" in Android settings
    echo 3. Tap the APK file to install
)
echo.
echo Press any key to exit...
pause >nul
