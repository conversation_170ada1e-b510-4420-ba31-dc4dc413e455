import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../widgets/debt_card.dart';
import '../utils/number_formatter.dart';

// كلاس لتنسيق الأرقام مع فاصل الآلاف
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  static const _separator = ',';

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // إزالة جميع الفواصل من النص الجديد
    final String newText = newValue.text.replaceAll(_separator, '');

    // التحقق من أن النص يحتوي على أرقام فقط
    if (newText.isEmpty) {
      return newValue.copyWith(text: '');
    }

    // التحقق من صحة الرقم
    if (double.tryParse(newText) == null) {
      return oldValue;
    }

    // تنسيق الرقم مع فاصل الآلاف
    final formatter = NumberFormat('#,###', 'en_US');
    final String formattedText = formatter.format(int.tryParse(newText) ?? 0);

    // حساب موضع المؤشر الجديد
    final int selectionIndex = formattedText.length;

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}

class DebtsTab extends StatefulWidget {
  const DebtsTab({super.key, required this.customer});
  final Customer customer;

  @override
  State<DebtsTab> createState() => _DebtsTabState();
}

// أنواع عرض البطاقات
enum DebtCardViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  mini, // عرض مصغر (5 بطاقات في صف)
  detailed, // عرض مفصل
  grid, // عرض شبكي (محفوظ للتوافق)
  timeline, // عرض زمني (محفوظ للتوافق)
}

class _DebtsTabState extends State<DebtsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isMultiSelectMode = false;
  final Set<int> _selectedDebtIds = {};
  String _multiSelectMode = 'payment'; // 'payment' أو 'delete'

  // نوع العرض الحالي
  DebtCardViewType _currentViewType = DebtCardViewType.standard;

  // دوال عامة يمكن استدعاؤها من الخارج
  void selectAllDebts(List<Debt> activeDebts) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedDebtIds.clear();
      for (final debt in activeDebts) {
        if (debt.id != null) {
          _selectedDebtIds.add(debt.id!);
        }
      }
    });
  }

  void payAllDebts(List<Debt> activeDebts, DebtProvider debtProvider) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedDebtIds.clear();
      for (final debt in activeDebts) {
        if (debt.id != null) {
          _selectedDebtIds.add(debt.id!);
        }
      }
    });
    _showPaySelectedDebtsDialog(context, debtProvider, activeDebts);
  }

  void deleteAllDebts(List<Debt> activeDebts, DebtProvider debtProvider) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedDebtIds.clear();
      for (final debt in activeDebts) {
        if (debt.id != null) {
          _selectedDebtIds.add(debt.id!);
        }
      }
    });
    _deleteSelectedDebts(context, debtProvider);
  }

  @override
  void initState() {
    super.initState();
    // تحميل نوع العرض المحفوظ
    _loadSavedViewType();

    // Ensure debts are loaded when tab is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      if (debtProvider.currentCustomerId != widget.customer.id) {
        debtProvider.loadCustomerDebts(widget.customer.id!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // فلترة الديون حسب العميل المحدد فقط
        final activeDebts = debtProvider.debts
            .where((debt) =>
                debt.customerId == widget.customer.id &&
                debt.status != DebtStatus.paid)
            .toList();

        // تنظيم الديون حسب التاريخ مثل نظرة عامة للديون
        final organizedDebts = _organizeDebtsByDate(activeDebts);

        if (activeDebts.isEmpty) {
          return RefreshIndicator(
            onRefresh: () async {
              await debtProvider.loadCustomerDebts(widget.customer.id!);
              await debtProvider.loadCustomerPayments(widget.customer.id!);
            },
            child: ListView(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.6,
                  child: Center(
                    child: _buildEmptyDebtsContent(debtProvider),
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // شريط العرض (فقط عندما لا نكون في وضع التحديد المتعدد)
            if (!_isMultiSelectMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // عنوان القسم
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.receipt_long,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'قائمة الديون (${activeDebts.length})',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر تغيير نوع العرض
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: PopupMenuButton<DebtCardViewType>(
                        padding: EdgeInsets.zero,
                        tooltip: 'تغيير نوع العرض',
                        onSelected: (DebtCardViewType viewType) {
                          setState(() {
                            _currentViewType = viewType;
                          });
                          // حفظ نوع العرض المختار
                          _saveViewType(viewType);
                        },
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            value: DebtCardViewType.standard,
                            child: _buildViewTypeMenuItem(
                              DebtCardViewType.standard,
                              Icons.view_agenda,
                              'عرض عادي',
                              'البطاقات العادية مع جميع التفاصيل',
                            ),
                          ),
                          PopupMenuItem(
                            value: DebtCardViewType.compact,
                            child: _buildViewTypeMenuItem(
                              DebtCardViewType.compact,
                              Icons.view_list,
                              'عرض مضغوط',
                              'بطاقات صغيرة مع المعلومات الأساسية',
                            ),
                          ),
                          PopupMenuItem(
                            value: DebtCardViewType.mini,
                            child: _buildViewTypeMenuItem(
                              DebtCardViewType.mini,
                              Icons.view_stream,
                              'عرض مصغر',
                              '5 بطاقات صغيرة في صف واحد',
                            ),
                          ),
                          PopupMenuItem(
                            value: DebtCardViewType.detailed,
                            child: _buildViewTypeMenuItem(
                              DebtCardViewType.detailed,
                              Icons.view_module,
                              'عرض مفصل',
                              'بطاقات كبيرة مع جميع التفاصيل',
                            ),
                          ),
                        ],
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getViewTypeIcon(_currentViewType),
                              color: Colors.grey[700],
                              size: 18,
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: Colors.grey[700],
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // شريط الأدوات (فقط في وضع التحديد المتعدد)
            if (_isMultiSelectMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _multiSelectMode == 'payment'
                        ? [Colors.green.shade600, Colors.green.shade700]
                        : [Colors.red.shade600, Colors.red.shade700],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (_multiSelectMode == 'payment'
                              ? Colors.green
                              : Colors.red)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // تحديد الكل
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _selectedDebtIds.clear();
                            for (final debt in activeDebts) {
                              if (debt.id != null) {
                                _selectedDebtIds.add(debt.id!);
                              }
                            }
                          });
                        },
                        icon: const Icon(Icons.select_all, size: 16),
                        label: const Text(
                          'تحديد الكل',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: _multiSelectMode == 'payment'
                              ? Colors.green.shade700
                              : Colors.red.shade700,
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 6,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 1,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),

                    // إلغاء الكل
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _selectedDebtIds.clear();
                          });
                        },
                        icon: const Icon(Icons.clear_all, size: 16),
                        label: const Text(
                          'إلغاء الكل',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: _multiSelectMode == 'payment'
                              ? Colors.green.shade700
                              : Colors.red.shade700,
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 6,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 1,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),

                    // زر العملية (تسديد أو حذف)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedDebtIds.isNotEmpty
                            ? () {
                                if (_multiSelectMode == 'payment') {
                                  _showPaySelectedDebtsDialog(
                                    context,
                                    debtProvider,
                                    activeDebts,
                                  );
                                } else {
                                  _deleteSelectedDebts(context, debtProvider);
                                }
                              }
                            : null,
                        icon: Icon(
                          _multiSelectMode == 'payment'
                              ? Icons.payment
                              : Icons.delete,
                          size: 16,
                        ),
                        label: Text(
                          _multiSelectMode == 'payment' ? 'تسديد' : 'حذف',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _multiSelectMode == 'payment'
                              ? Colors.green.shade800
                              : Colors.red.shade800,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 6,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Debts List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerDebts(widget.customer.id!);
                },
                child: _buildDebtsView(organizedDebts),
              ),
            ),

            // شريط المعلومات في الأسفل (فقط في وضع التحديد المتعدد)
            if (_isMultiSelectMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.grey.shade100, Colors.grey.shade200],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  border: Border(top: BorderSide(color: Colors.grey.shade300)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: Row(
                    children: [
                      // أيقونة المعلومات
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _multiSelectMode == 'payment'
                              ? Colors.green.shade100
                              : Colors.red.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _multiSelectMode == 'payment'
                                ? Colors.green.shade300
                                : Colors.red.shade300,
                          ),
                        ),
                        child: Icon(
                          Icons.info_outline,
                          color: _multiSelectMode == 'payment'
                              ? Colors.green.shade700
                              : Colors.red.shade700,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),

                      // معلومات التحديد
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'تم تحديد ${_selectedDebtIds.length} دين',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'المبلغ الإجمالي: ${NumberFormatter.formatCurrency(_calculateSelectedDebtsTotal(activeDebts))}',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: _multiSelectMode == 'payment'
                                    ? Colors.green.shade700
                                    : Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر الإغلاق
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _isMultiSelectMode = false;
                            _selectedDebtIds.clear();
                          });
                        },
                        icon: const Icon(Icons.close, size: 18),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey.shade300,
                          foregroundColor: Colors.grey.shade700,
                          padding: const EdgeInsets.all(8),
                          minimumSize: const Size(36, 36),
                        ),
                        tooltip: 'إلغاء التحديد',
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  // تنظيم الديون حسب التاريخ (مثل نظرة عامة للديون)
  Map<String, List<Debt>> _organizeDebtsByDate(List<Debt> debts) {
    final Map<String, List<Debt>> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeek = today.subtract(const Duration(days: 7));
    final thisMonth = DateTime(now.year, now.month);

    for (final debt in debts) {
      final debtDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      String category;

      if (debtDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (debtDate.isAtSameMomentAs(yesterday)) {
        category = 'أمس';
      } else if (debtDate.isAfter(thisWeek)) {
        category = 'هذا الأسبوع';
      } else if (debtDate.isAfter(thisMonth)) {
        category = 'هذا الشهر';
      } else {
        category = 'منتهي الموعد';
      }

      organized.putIfAbsent(category, () => []);
      organized[category]!.add(debt);
    }

    // ترتيب الفئات حسب الأولوية
    final orderedCategories = [
      'اليوم',
      'أمس',
      'هذا الأسبوع',
      'هذا الشهر',
      'منتهي الموعد',
    ];
    final Map<String, List<Debt>> orderedResult = {};

    for (final category in orderedCategories) {
      if (organized.containsKey(category)) {
        // ترتيب الديون داخل كل فئة حسب التاريخ (الأحدث أولاً)
        organized[category]!.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        orderedResult[category] = organized[category]!;
      }
    }

    return orderedResult;
  }

  // Calculate total amount of selected debts
  double _calculateSelectedDebtsTotal(List<Debt> activeDebts) {
    double total = 0.0;
    for (final debt in activeDebts) {
      if (_selectedDebtIds.contains(debt.id)) {
        total += debt.remainingAmount;
      }
    }
    return total;
  }

  // Delete selected debts
  Future<void> _deleteSelectedDebts(
    BuildContext context,
    DebtProvider debtProvider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_forever,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'تأكيد حذف المحدد',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف ${_selectedDebtIds.length} دين محدد؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context, false),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context, true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف المحدد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (confirmed == true) {
      try {
        for (final debtId in _selectedDebtIds) {
          await debtProvider.deleteDebt(debtId);
        }

        setState(() {
          _selectedDebtIds.clear();
          _isMultiSelectMode = false;
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الديون: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Show payment dialog for selected debts
  Future<void> _showPaySelectedDebtsDialog(
    BuildContext context,
    DebtProvider debtProvider,
    List<Debt> activeDebts,
  ) async {
    final selectedDebts = activeDebts
        .where((debt) => _selectedDebtIds.contains(debt.id))
        .toList();

    final totalAmount = _calculateSelectedDebtsTotal(activeDebts);
    bool isFullPayment = true;
    final amountController = TextEditingController(
      text: NumberFormat('#,###', 'en_US').format(totalAmount.toInt()),
    );
    final notesController = TextEditingController();
    final DateTime selectedDate = DateTime.now();

    // متغير لتحديد الدين الذي سيستكمل المبلغ منه
    Debt? selectedDebtForRemainder =
        selectedDebts.isNotEmpty ? selectedDebts.first : null;

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 40,
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.95,
            height: MediaQuery.of(context).size.height * 0.85,
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.green.shade50],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.green, Colors.green.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.payment,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            children: [
                              const Text(
                                'تسديد الديون المحددة',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                '${selectedDebts.length} دين محدد',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context, false),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment Type Selector
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = true;
                                  amountController.text = NumberFormat(
                                    '#,###',
                                    'en_US',
                                  ).format(totalAmount.toInt());
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    bottomLeft: Radius.circular(12),
                                  ),
                                  gradient: isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.green,
                                            Colors.green.shade600,
                                          ],
                                        )
                                      : null,
                                  color:
                                      isFullPayment ? null : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payment,
                                      color: isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد كامل',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = false;
                                  amountController.clear();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  ),
                                  gradient: !isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.orange,
                                            Colors.orange.shade600,
                                          ],
                                        )
                                      : null,
                                  color: !isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payments,
                                      color: !isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد جزئي',
                                      style: TextStyle(
                                        color: !isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Summary Card
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ الإجمالي:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(totalAmount),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Amount Field - قابل للتعديل دائماً
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.shade300,
                          width: 2,
                        ),
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          ThousandsSeparatorInputFormatter(),
                        ],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد (قابل للتعديل)',
                          labelStyle: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: Icon(
                            Icons.edit,
                            color: Colors.blue.shade600,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                        onChanged: (value) {
                          // إزالة الفواصل للحصول على الرقم الفعلي
                          final cleanValue = value.replaceAll(',', '');
                          final amount = double.tryParse(cleanValue);
                          if (amount != null) {
                            setState(() {
                              isFullPayment = amount >= totalAmount;
                            });
                          }
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // قائمة الديون المحددة - تصميم احترافي
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        border: Border.all(
                          color: Colors.blue.shade200,
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Header احترافي
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.blue.shade600,
                                  Colors.blue.shade700,
                                ],
                              ),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.account_balance_wallet,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    children: [
                                      const Text(
                                        'الديون المحددة للتسديد',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      Text(
                                        '${selectedDebts.length} دين • ${NumberFormatter.formatCurrency(totalAmount)}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    '${selectedDebts.length}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // قائمة الديون المحسنة
                          Container(
                            constraints: const BoxConstraints(maxHeight: 200),
                            child: ListView.separated(
                              shrinkWrap: true,
                              padding: const EdgeInsets.all(12),
                              itemCount: selectedDebts.length,
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 8),
                              itemBuilder: (context, index) {
                                final debt = selectedDebts[index];
                                final isSelected =
                                    selectedDebtForRemainder?.id == debt.id;

                                return Container(
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.white
                                        : Colors.white.withValues(alpha: 0.7),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isSelected
                                          ? Colors.blue.shade400
                                          : Colors.blue.shade200,
                                      width: isSelected ? 2 : 1,
                                    ),
                                    boxShadow: isSelected
                                        ? [
                                            BoxShadow(
                                              color: Colors.blue.withValues(
                                                alpha: 0.2,
                                              ),
                                              blurRadius: 8,
                                              offset: const Offset(0, 2),
                                            ),
                                          ]
                                        : null,
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        selectedDebtForRemainder = debt;
                                      });
                                    },
                                    borderRadius: BorderRadius.circular(12),
                                    child: Padding(
                                      padding: const EdgeInsets.all(12),
                                      child: Row(
                                        children: [
                                          // Radio Button مخصص
                                          Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: isSelected
                                                    ? Colors.blue.shade600
                                                    : Colors.grey.shade400,
                                                width: 2,
                                              ),
                                              color: isSelected
                                                  ? Colors.blue.shade600
                                                  : Colors.transparent,
                                            ),
                                            child: isSelected
                                                ? const Icon(
                                                    Icons.check,
                                                    color: Colors.white,
                                                    size: 12,
                                                  )
                                                : null,
                                          ),
                                          const SizedBox(width: 12),

                                          // معلومات الدين
                                          Expanded(
                                            child: Column(
                                              children: [
                                                // نوع البطاقة الأصلي واسم العميل
                                                Column(
                                                  children: [
                                                    // نوع البطاقة الأصلي
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.credit_card,
                                                          size: 14,
                                                          color: isSelected
                                                              ? Colors
                                                                  .blue.shade700
                                                              : Colors.orange
                                                                  .shade600,
                                                        ),
                                                        const SizedBox(
                                                          width: 4,
                                                        ),
                                                        Text(
                                                          _convertCardTypeToArabic(
                                                            debt.cardType,
                                                          ),
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: isSelected
                                                                ? Colors.blue
                                                                    .shade800
                                                                : Colors.orange
                                                                    .shade700,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    const SizedBox(height: 2),
                                                    // اسم العميل
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.person,
                                                          size: 12,
                                                          color: isSelected
                                                              ? Colors
                                                                  .blue.shade600
                                                              : Colors.grey
                                                                  .shade600,
                                                        ),
                                                        const SizedBox(
                                                          width: 4,
                                                        ),
                                                        Text(
                                                          widget.customer.name,
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color: isSelected
                                                                ? Colors.blue
                                                                    .shade700
                                                                : Colors.grey
                                                                    .shade700,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 6),

                                                // المبلغ والكمية
                                                Row(
                                                  children: [
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                        vertical: 2,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: isSelected
                                                            ? Colors
                                                                .green.shade100
                                                            : Colors
                                                                .grey.shade100,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                          6,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        NumberFormatter
                                                            .formatCurrency(
                                                          debt.remainingAmount,
                                                        ),
                                                        style: TextStyle(
                                                          fontSize: 11,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: isSelected
                                                              ? Colors.green
                                                                  .shade700
                                                              : Colors.grey
                                                                  .shade700,
                                                        ),
                                                      ),
                                                    ),
                                                    if (debt.quantity > 1) ...[
                                                      const SizedBox(width: 8),
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 6,
                                                          vertical: 2,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: isSelected
                                                              ? Colors.orange
                                                                  .shade100
                                                              : Colors.grey
                                                                  .shade100,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                            6,
                                                          ),
                                                        ),
                                                        child: Text(
                                                          'الكمية ${debt.quantity}',
                                                          style: TextStyle(
                                                            fontSize: 10,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color: isSelected
                                                                ? Colors.orange
                                                                    .shade700
                                                                : Colors.grey
                                                                    .shade600,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ],
                                                ),
                                                const SizedBox(height: 4),

                                                // التواريخ
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.calendar_today,
                                                      size: 10,
                                                      color:
                                                          Colors.grey.shade500,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      _formatDate(
                                                        debt.entryDate,
                                                      ),
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                        color: Colors
                                                            .grey.shade600,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Icon(
                                                      Icons.schedule,
                                                      size: 10,
                                                      color: Colors
                                                          .orange.shade400,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      _formatDate(debt.dueDate),
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                        color: Colors
                                                            .orange.shade600,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),

                                          // مؤشر التحديد
                                          if (isSelected)
                                            Container(
                                              padding: const EdgeInsets.all(4),
                                              decoration: BoxDecoration(
                                                color: Colors.blue.shade600,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.check,
                                                color: Colors.white,
                                                size: 12,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),

                          // معلومات الدين المحدد للاستكمال
                          if (selectedDebtForRemainder != null)
                            Container(
                              margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.green.shade50,
                                    Colors.green.shade100,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.green.shade300,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade600,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.trending_up,
                                      color: Colors.white,
                                      size: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          'دين الاستكمال المحدد',
                                          style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green.shade800,
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.credit_card,
                                              size: 14,
                                              color: Colors.green.shade700,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              _convertCardTypeToArabic(
                                                selectedDebtForRemainder!
                                                    .cardType,
                                              ),
                                              style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.green.shade800,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Icon(
                                              Icons.person,
                                              size: 12,
                                              color: Colors.green.shade600,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              widget.customer.name,
                                              style: TextStyle(
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.green.shade700,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                          'قيد: ${_formatDate(selectedDebtForRemainder!.entryDate)} • استحقاق: ${_formatDate(selectedDebtForRemainder!.dueDate)}',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.green.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 2,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 15,
                          ),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context, false),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context, true),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'تأكيد التسديد',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    if (confirmed == true) {
      // إزالة الفواصل من النص للحصول على الرقم الفعلي
      final cleanText = amountController.text.replaceAll(',', '');
      final amount = double.tryParse(cleanText);
      if (amount == null || amount <= 0) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى إدخال مبلغ صحيح'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      try {
        // منطق التسديد المحسن
        if (isFullPayment || amount >= totalAmount) {
          // تسديد كامل - سدد كل الديون بالكامل
          for (final debt in selectedDebts) {
            await debtProvider.makePayment(
              debt.id!,
              debt.remainingAmount,
              PaymentType.full,
              notesController.text.trim().isEmpty
                  ? null
                  : notesController.text.trim(),
              paymentDate: selectedDate,
            );
          }
        } else {
          // تسديد جزئي - وزع المبلغ على الديون
          double remainingAmount = amount;

          // سدد الديون الأخرى أولاً (ما عدا الدين المحدد لاستكمال المبلغ)
          for (final debt in selectedDebts) {
            if (debt.id == selectedDebtForRemainder?.id) continue;

            final paymentAmount = math.min(
              remainingAmount,
              debt.remainingAmount,
            );
            if (paymentAmount > 0) {
              await debtProvider.makePayment(
                debt.id!,
                paymentAmount,
                paymentAmount >= debt.remainingAmount
                    ? PaymentType.full
                    : PaymentType.partial,
                notesController.text.trim().isEmpty
                    ? null
                    : notesController.text.trim(),
                paymentDate: selectedDate,
              );
              remainingAmount -= paymentAmount;
            }
          }

          // سدد المبلغ المتبقي من الدين المحدد
          if (remainingAmount > 0 && selectedDebtForRemainder != null) {
            final paymentAmount = math.min(
              remainingAmount,
              selectedDebtForRemainder!.remainingAmount,
            );
            if (paymentAmount > 0) {
              await debtProvider.makePayment(
                selectedDebtForRemainder!.id!,
                paymentAmount,
                paymentAmount >= selectedDebtForRemainder!.remainingAmount
                    ? PaymentType.full
                    : PaymentType.partial,
                notesController.text.trim().isEmpty
                    ? null
                    : notesController.text.trim(),
                paymentDate: selectedDate,
              );
            }
          }
        }

        setState(() {
          _selectedDebtIds.clear();
          _isMultiSelectMode = false;
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسديد الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في التسديد: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // دالة لتنسيق التاريخ
  String _formatDate(DateTime date) {
    final formatter = DateFormat('yyyy/MM/dd', 'en');
    return formatter.format(date);
  }

  // دالة لتحويل أسماء البطاقات إلى العربية
  String _convertCardTypeToArabic(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    // التعامل مع البطاقات المخصصة باستخدام CardTypeProvider
    if (cleanType.startsWith('custom_')) {
      try {
        final cardTypeProvider = Provider.of<CardTypeProvider>(
          context,
          listen: false,
        );

        // استخراج الرقم من custom_X
        final cardTypeId = int.parse(cleanType.replaceFirst('custom_', ''));

        // البحث عن البطاقة المخصصة
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );

        return customCardType.displayName;
      } catch (e) {
        // في حالة عدم العثور على البطاقة، استخدم الأسماء الافتراضية
        final customNumber = cleanType.replaceAll('custom_', '');
        switch (customNumber) {
          case '1':
          case '3':
            return 'أبو الستة'; // فئة 6000
          case '2':
          case '4':
            return 'آسيا'; // فئة 5000
          case '5':
          case '6':
            return 'زين'; // فئة 5000
          case '7':
          case '8':
            return 'أبو العشرة'; // فئة 10000
          default:
            return 'بطاقة مخصصة $customNumber';
        }
      }
    }

    // التعامل مع الأنواع الافتراضية
    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia':
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.isNotEmpty ? cardType : 'غير محدد';
        }
        // إذا كان إنجليزي ولم نجده، أعده كما هو
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }

  // دالة للتحقق من النص العربي
  bool _isArabicText(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // دالة لبناء عرض الديون حسب النوع المحدد مع التصنيف
  Widget _buildDebtsView(Map<String, List<Debt>> organizedDebts) {
    if (organizedDebts.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد ديون',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(8),
      children: organizedDebts.entries.map((entry) {
        final dateCategory = entry.key;
        final categoryDebts = entry.value;

        return _buildDateCategorySection(dateCategory, categoryDebts);
      }).toList(),
    );
  }

  // بناء قسم فئة التاريخ
  Widget _buildDateCategorySection(String dateCategory, List<Debt> debts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الفئة
        _buildCategoryHeader(dateCategory, debts.length),
        const SizedBox(height: 8),

        // بطاقات الديون حسب نوع العرض
        _buildCategoryDebtsView(debts),
        const SizedBox(height: 16),
      ],
    );
  }

  // بناء عنوان الفئة
  Widget _buildCategoryHeader(String category, int count) {
    Color categoryColor;
    IconData categoryIcon;

    switch (category) {
      case 'اليوم':
        categoryColor = Colors.green;
        categoryIcon = Icons.today;
        break;
      case 'أمس':
        categoryColor = const Color(0xFF1A237E); // نفس لون شريط بطاقة الأمس
        categoryIcon = Icons.schedule;
        break;
      case 'هذا الأسبوع':
        categoryColor = Colors.blue;
        categoryIcon = Icons.date_range;
        break;
      case 'هذا الشهر':
        categoryColor = Colors.purple;
        categoryIcon = Icons.calendar_month;
        break;
      default:
        categoryColor = const Color(0xFF8B0000); // نفس لون شريط البطاقة الحمراء
        categoryIcon = Icons.history;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: categoryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: categoryColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(categoryIcon, color: categoryColor, size: 18),
          const SizedBox(width: 8),
          Text(
            category,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: categoryColor,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: categoryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$count',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء عرض ديون الفئة حسب نوع العرض
  Widget _buildCategoryDebtsView(List<Debt> debts) {
    switch (_currentViewType) {
      case DebtCardViewType.standard:
        return _buildStandardCategoryView(debts);
      case DebtCardViewType.compact:
        return _buildCompactCategoryView(debts);
      case DebtCardViewType.mini:
        return _buildMiniCategoryView(debts);
      case DebtCardViewType.detailed:
        return _buildDetailedCategoryView(debts);
      case DebtCardViewType.grid:
        return _buildStandardCategoryView(debts);
      case DebtCardViewType.timeline:
        return _buildStandardCategoryView(debts);
    }
  }

  // العرض العادي للفئة
  Widget _buildStandardCategoryView(List<Debt> debts) {
    return Column(
      children: debts.map((debt) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: GestureDetector(
            onLongPress: () {
              setState(() {
                _isMultiSelectMode = true;
                _multiSelectMode = 'payment';
                _selectedDebtIds.clear();
                if (debt.id != null) {
                  _selectedDebtIds.add(debt.id!);
                }
              });
            },
            child: DebtCard(
              debt: debt,
              isSelected: _selectedDebtIds.contains(debt.id),
              isMultiSelectMode: _isMultiSelectMode,
              isMiniView: _currentViewType == DebtCardViewType.mini,
              onStartMultiSelectForPayment: () {
                setState(() {
                  _isMultiSelectMode = true;
                  _multiSelectMode = 'payment';
                  _selectedDebtIds.clear();
                  if (debt.id != null) {
                    _selectedDebtIds.add(debt.id!);
                  }
                });
              },
              onStartMultiSelectForDelete: () {
                setState(() {
                  _isMultiSelectMode = true;
                  _multiSelectMode = 'delete';
                  _selectedDebtIds.clear();
                  if (debt.id != null) {
                    _selectedDebtIds.add(debt.id!);
                  }
                });
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  // العرض المضغوط للفئة - عرض مضغوط حقيقي
  Widget _buildCompactCategoryView(List<Debt> debts) {
    return Column(
      children: debts.map((debt) => _buildCompactDebtCard(debt, 0)).toList(),
    );
  }

  // العرض المصغر للفئة
  Widget _buildMiniCategoryView(List<Debt> debts) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.68,
      ),
      itemCount: debts.length,
      itemBuilder: (context, index) {
        final debt = debts[index];
        return GestureDetector(
          onLongPress: _isMultiSelectMode
              ? null
              : () {
                  setState(() {
                    _isMultiSelectMode = true;
                    _multiSelectMode = 'payment';
                    _selectedDebtIds.clear();
                    if (debt.id != null) {
                      _selectedDebtIds.add(debt.id!);
                    }
                  });
                },
          onTap: _isMultiSelectMode
              ? () {
                  setState(() {
                    if (_selectedDebtIds.contains(debt.id)) {
                      _selectedDebtIds.remove(debt.id);
                    } else if (debt.id != null) {
                      _selectedDebtIds.add(debt.id!);
                    }
                  });
                }
              : null,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: _selectedDebtIds.contains(debt.id)
                  ? Border.all(color: Colors.blue, width: 2)
                  : null,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Transform.scale(
              scale: 1.0,
              child: DebtCard(
                debt: debt,
                isSelected: _selectedDebtIds.contains(debt.id),
                isMultiSelectMode: _isMultiSelectMode,
                isGridView: true,
                isMiniView: _currentViewType == DebtCardViewType.mini,
                onStartMultiSelectForPayment: () {
                  setState(() {
                    _isMultiSelectMode = true;
                    _multiSelectMode = 'payment';
                    _selectedDebtIds.clear();
                    if (debt.id != null) {
                      _selectedDebtIds.add(debt.id!);
                    }
                  });
                },
                onStartMultiSelectForDelete: () {
                  setState(() {
                    _isMultiSelectMode = true;
                    _multiSelectMode = 'delete';
                    _selectedDebtIds.clear();
                    if (debt.id != null) {
                      _selectedDebtIds.add(debt.id!);
                    }
                  });
                },
              ),
            ),
          ),
        );
      },
    );
  }

  // العرض المفصل للفئة
  Widget _buildDetailedCategoryView(List<Debt> debts) {
    return Column(
      children: debts.map((debt) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.shade200, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: DebtCard(
            debt: debt,
            isSelected: _selectedDebtIds.contains(debt.id),
            isMultiSelectMode: _isMultiSelectMode,
            isMiniView: _currentViewType == DebtCardViewType.mini,
            onStartMultiSelectForPayment: () {
              setState(() {
                _isMultiSelectMode = true;
                _multiSelectMode = 'payment';
                _selectedDebtIds.clear();
                if (debt.id != null) {
                  _selectedDebtIds.add(debt.id!);
                }
              });
            },
            onStartMultiSelectForDelete: () {
              setState(() {
                _isMultiSelectMode = true;
                _multiSelectMode = 'delete';
                _selectedDebtIds.clear();
                if (debt.id != null) {
                  _selectedDebtIds.add(debt.id!);
                }
              });
            },
          ),
        );
      }).toList(),
    );
  }

  // دالة للحصول على أيقونة نوع العرض
  IconData _getViewTypeIcon(DebtCardViewType viewType) {
    switch (viewType) {
      case DebtCardViewType.standard:
        return Icons.view_agenda;
      case DebtCardViewType.compact:
        return Icons.view_list;
      case DebtCardViewType.mini:
        return Icons.view_stream;
      case DebtCardViewType.detailed:
        return Icons.view_module;
      case DebtCardViewType.grid:
        return Icons.grid_view;
      case DebtCardViewType.timeline:
        return Icons.timeline;
    }
  }

  // دالة لبناء بطاقة دين مضغوطة - تصميم محترف وقيم
  Widget _buildCompactDebtCard(Debt debt, int index) {
    final isSelected = _selectedDebtIds.contains(debt.id);
    final isOverdue = debt.dueDate.isBefore(DateTime.now());
    final daysSinceEntry = DateTime.now().difference(debt.entryDate).inDays;
    final daysUntilDue = debt.dueDate.difference(DateTime.now()).inDays;

    return GestureDetector(
      onLongPress: () {
        setState(() {
          _isMultiSelectMode = true;
          _multiSelectMode = 'payment';
          _selectedDebtIds.clear();
          if (debt.id != null) {
            _selectedDebtIds.add(debt.id!);
          }
        });
      },
      onTap: _isMultiSelectMode
          ? () {
              setState(() {
                if (debt.id != null) {
                  if (_selectedDebtIds.contains(debt.id!)) {
                    _selectedDebtIds.remove(debt.id!);
                    if (_selectedDebtIds.isEmpty) {
                      _isMultiSelectMode = false;
                    }
                  } else {
                    _selectedDebtIds.add(debt.id!);
                  }
                }
              });
            }
          : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [Colors.blue.shade50, Colors.blue.shade100],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [Colors.white, Colors.grey.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.blue.shade300
                : isOverdue
                    ? Colors.red.shade200
                    : Colors.grey.shade200,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isSelected ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // الصف الأول: أيقونة + نوع الكارت + حالة الاستحقاق
                Row(
                  children: [
                    // أيقونة محسنة مع تدرج
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.shade400,
                            Colors.orange.shade600
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.credit_card,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // نوع الكارت
                    Expanded(
                      child: Text(
                        _convertCardTypeToArabic(debt.cardType),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? Colors.blue.shade800
                              : Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // مؤشر حالة الاستحقاق
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isOverdue
                            ? Colors.red.shade100
                            : daysUntilDue <= 3
                                ? Colors.orange.shade100
                                : Colors.green.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isOverdue
                              ? Colors.red.shade300
                              : daysUntilDue <= 3
                                  ? Colors.orange.shade300
                                  : Colors.green.shade300,
                        ),
                      ),
                      child: Text(
                        isOverdue
                            ? 'متأخر'
                            : daysUntilDue <= 0
                                ? 'اليوم'
                                : daysUntilDue <= 3
                                    ? '$daysUntilDueد'
                                    : 'عادي',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: isOverdue
                              ? Colors.red.shade700
                              : daysUntilDue <= 3
                                  ? Colors.orange.shade700
                                  : Colors.green.shade700,
                        ),
                      ),
                    ),

                    // مؤشر التحديد
                    if (_isMultiSelectMode) ...[
                      const SizedBox(width: 8),
                      Icon(
                        isSelected
                            ? Icons.check_circle
                            : Icons.radio_button_unchecked,
                        color: isSelected
                            ? Colors.blue.shade600
                            : Colors.grey.shade400,
                        size: 20,
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 10),

                // الصف الثاني: الكمية والمبلغ في بطاقات متقابلة
                Row(
                  children: [
                    // بطاقة الكمية
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.inventory_2_outlined,
                                  size: 14,
                                  color: Colors.blue.shade600,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'الكمية:',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${debt.quantity}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // بطاقة المبلغ المتبقي
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: debt.remainingAmount > 0
                                ? Colors.red.shade300
                                : Colors.green.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: (debt.remainingAmount > 0
                                      ? Colors.red
                                      : Colors.green)
                                  .withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  'المتبقي:',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.attach_money,
                                  size: 14,
                                  color: debt.remainingAmount > 0
                                      ? Colors.red.shade600
                                      : Colors.green.shade600,
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${(debt.remainingAmount / 1000).toStringAsFixed(3)} ألف',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: debt.remainingAmount > 0
                                    ? Colors.red.shade700
                                    : Colors.green.shade700,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // الصف الثالث: بطاقات التواريخ متقابلة
                Row(
                  children: [
                    // بطاقة تاريخ القيد
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 12,
                                  color: Colors.blue.shade600,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'تاريخ القيد:',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _formatFullDate(debt.entryDate),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                            Text(
                              _getDayName(debt.entryDate),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            if (daysSinceEntry > 0)
                              Text(
                                'منذ ${_formatTimePeriod(daysSinceEntry)}',
                                style: TextStyle(
                                  fontSize: 9,
                                  color: Colors.grey.shade500,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // بطاقة تاريخ الاستحقاق
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isOverdue
                                ? Colors.red.shade300
                                : daysUntilDue <= 3
                                    ? Colors.orange.shade300
                                    : Colors.green.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: (isOverdue
                                      ? Colors.red
                                      : daysUntilDue <= 3
                                          ? Colors.orange
                                          : Colors.green)
                                  .withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  'تاريخ الاستحقاق:',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.schedule,
                                  size: 12,
                                  color: isOverdue
                                      ? Colors.red.shade600
                                      : daysUntilDue <= 3
                                          ? Colors.orange.shade600
                                          : Colors.green.shade600,
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _formatFullDate(debt.dueDate),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: isOverdue
                                    ? Colors.red.shade700
                                    : daysUntilDue <= 3
                                        ? Colors.orange.shade700
                                        : Colors.green.shade700,
                              ),
                              textAlign: TextAlign.end,
                            ),
                            Text(
                              _getDayName(debt.dueDate),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey.shade600,
                              ),
                              textAlign: TextAlign.end,
                            ),
                            Text(
                              isOverdue
                                  ? 'متأخر ${_formatTimePeriod(-daysUntilDue)}'
                                  : daysUntilDue == 0
                                      ? 'مستحق اليوم'
                                      : 'باقي ${_formatTimePeriod(daysUntilDue)}',
                              style: TextStyle(
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                                color: isOverdue
                                    ? Colors.red.shade600
                                    : daysUntilDue <= 3
                                        ? Colors.orange.shade600
                                        : Colors.green.shade600,
                                fontStyle: FontStyle.italic,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 10),

                // الصف الرابع: معلومات إضافية (نوع الكارت + الملاحظات)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      // نوع الكارت والمبلغ الأصلي
                      Row(
                        children: [
                          Icon(
                            Icons.credit_card_outlined,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'نوع الكارت:',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _convertCardTypeToArabic(debt.cardType),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade800,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'أصلي: ${(debt.amount / 1000).toStringAsFixed(3)} ألف',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 6),

                      // اسم الصنف والملاحظات
                      Row(
                        children: [
                          Icon(
                            Icons.inventory_2,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'الصنف:',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              debt.itemName.isNotEmpty
                                  ? debt.itemName
                                  : 'غير محدد',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade800,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      // الملاحظات إذا وجدت
                      if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                        const SizedBox(height: 6),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.note_outlined,
                                    size: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'ملاحظات:',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 2),
                              Text(
                                debt.notes!,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey.shade800,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دالة لتنسيق التاريخ بشكل كامل
  String _formatFullDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // دالة للحصول على اسم اليوم
  String _getDayName(DateTime date) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return dayNames[date.weekday - 1];
  }

  // دالة لتنسيق فترة زمنية
  String _formatTimePeriod(int days) {
    if (days == 0) {
      return 'اليوم';
    } else if (days == 1) {
      return 'يوم واحد';
    } else if (days == 2) {
      return 'يومين';
    } else if (days <= 10) {
      return '$days أيام';
    } else if (days <= 30) {
      return '$days يوماً';
    } else {
      final months = (days / 30).floor();
      final remainingDays = days % 30;
      if (remainingDays == 0) {
        return months == 1 ? 'شهر واحد' : '$months أشهر';
      } else {
        return months == 1
            ? 'شهر و$remainingDays يوماً'
            : '$months أشهر و$remainingDays يوماً';
      }
    }
  }

  // دالة لبناء عنصر قائمة نوع العرض
  Widget _buildViewTypeMenuItem(
    DebtCardViewType viewType,
    IconData icon,
    String title,
    String description,
  ) {
    final isSelected = _currentViewType == viewType;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: Colors.blue.withValues(alpha: 0.3))
            : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: isSelected ? Colors.blue[700] : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                    color: isSelected ? Colors.blue[700] : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.blue[600] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(Icons.check_circle, color: Colors.blue[700], size: 18),
        ],
      ),
    );
  }

  // دالة لحفظ نوع العرض المختار
  Future<void> _saveViewType(DebtCardViewType viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('debt_view_type', viewType.name);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع العرض: $e');
    }
  }

  // دالة لتحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewType = prefs.getString('debt_view_type');

      if (savedViewType != null) {
        final viewType = DebtCardViewType.values.firstWhere(
          (e) => e.name == savedViewType,
          orElse: () => DebtCardViewType.standard,
        );

        setState(() {
          _currentViewType = viewType;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع العرض: $e');
    }
  }

  // بناء محتوى الديون الفارغة
  Widget _buildEmptyDebtsContent(DebtProvider debtProvider) {
    // التحقق من وجود مدفوعات للعميل
    final payments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (payments.isNotEmpty) {
      // ترتيب المدفوعات حسب التاريخ (الأحدث أولاً)
      payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
      final lastPayment = payments.first;

      // العميل سدد كامل ديونه - عرض آخر عملية تسديد
      return _buildLastPaymentDisplay(lastPayment);
    } else {
      // لا توجد ديون ولا مدفوعات
      return _buildNoDebtsDisplay();
    }
  }

  // بناء عرض آخر عملية تسديد
  Widget _buildLastPaymentDisplay(Payment lastPayment) {
    final DateTime paymentDate = lastPayment.paymentDate;

    // تنسيق التاريخ مع اسم اليوم
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    final dayName = arabicDays[paymentDate.weekday - 1];
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');

    // تنسيق الوقت 12 ساعة مع صباحاً/مساءً
    String formatTime12Hour(DateTime date) {
      final hour = date.hour;
      final minute = date.minute;

      if (hour == 0) {
        return '12:${minute.toString().padLeft(2, '0')} منتصف الليل';
      } else if (hour < 12) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} صباحاً';
      } else if (hour == 12) {
        return '12:${minute.toString().padLeft(2, '0')} ظهراً';
      } else {
        final hour12 = hour - 12;
        return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} مساءً';
      }
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // أيقونة التسديد الكامل
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade400, Colors.green.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.green.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.check_circle,
            color: Colors.white,
            size: 50,
          ),
        ),
        const SizedBox(height: 24),

        // عنوان التسديد الكامل
        const Text(
          'تم التسديد الكامل',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 16),

        // بطاقة آخر عملية تسديد
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 32),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.shade200, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // عنوان البطاقة
              Row(
                children: [
                  Icon(Icons.payment, color: Colors.green.shade600, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    'آخر عملية تسديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // المبلغ
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'المبلغ:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    NumberFormatter.formatCurrency(lastPayment.amount),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // التاريخ مع اسم اليوم
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'التاريخ:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '$dayName - ${dateFormat.format(paymentDate)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // الوقت
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الوقت:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    formatTime12Hour(paymentDate),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // نص إرشادي
        Text(
          'اسحب للأسفل للتحديث',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  // بناء عرض عدم وجود ديون
  Widget _buildNoDebtsDisplay() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.receipt_long_outlined,
          size: 80,
          color: Colors.grey[400],
        ),
        const SizedBox(height: 16),
        Text(
          'لا توجد ديون',
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اضغط على زر + لإضافة دين جديد',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'اسحب للأسفل للتحديث',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[400],
          ),
        ),
      ],
    );
  }
}
