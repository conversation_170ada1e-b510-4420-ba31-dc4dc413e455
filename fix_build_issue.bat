@echo off
echo ========================================
echo    إصلاح مشكلة البناء النهائي
echo ========================================
echo.

echo 🛑 إيقاف جميع عمليات Flutter...
taskkill /f /im flutter.exe >nul 2>&1
taskkill /f /im dart.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1

echo.
echo 🧹 تنظيف شامل للمشروع...
if exist "build" rmdir /s /q "build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"
if exist "android\.gradle" rmdir /s /q "android\.gradle"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist ".flutter-plugins" del ".flutter-plugins"
if exist ".flutter-plugins-dependencies" del ".flutter-plugins-dependencies"
if exist "pubspec.lock" del "pubspec.lock"

echo.
echo 🔧 إصلاح Flutter cache...
flutter clean
flutter pub cache repair

echo.
echo 📦 تحديث التبعيات...
flutter pub get

echo.
echo 🚀 تشغيل على Chrome (أسرع)...
flutter run -d chrome --debug

echo.
echo إذا لم يعمل على Chrome، جرب:
echo flutter run -d windows --debug
echo.
pause
