import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'package:timezone/timezone.dart' as tz;
import 'dart:typed_data';
import '../models/notification_model.dart';
import '../models/customer.dart';
import '../database/database_helper.dart';

class LocalNotificationService {
  factory LocalNotificationService() => _instance;
  LocalNotificationService._internal();
  static final LocalNotificationService _instance =
      LocalNotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final AudioPlayer _audioPlayer = AudioPlayer();

  bool _isInitialized = false;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // خريطة لحفظ أرقام هواتف العملاء مؤقتاً
  final Map<String, String> _customerPhones = {};

  // خريطة لحفظ معلومات البطاقات مؤقتاً
  final Map<String, Map<String, String>> _cardInfoCache = {};

  // عداد للمعرفات الآمنة
  static int _notificationIdCounter = 1000;

  // دالة لتوليد معرف آمن للإشعارات (32-bit integer)
  int _generateSafeNotificationId([String? prefix]) {
    _notificationIdCounter++;
    if (_notificationIdCounter > 2147483647) {
      _notificationIdCounter = 1000; // إعادة تعيين العداد
    }

    // إضافة hash للبادئة إذا وجدت
    if (prefix != null) {
      final hash = prefix.hashCode.abs() % 10000;
      return (_notificationIdCounter + hash) % 2147483647;
    }

    return _notificationIdCounter;
  }

  // تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة timezone أولاً
      await _initializeTimezone();

      // تحميل الإعدادات
      await _loadSettings();

      // إعدادات الأندرويد
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/launcher_icon');

      // إعدادات iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings();

      // الإعدادات العامة
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // تهيئة المكتبة
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // طلب الأذونات
      await _requestPermissions();

      _isInitialized = true;
      debugPrint('✅ Local Notification Service initialized');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
      _isInitialized = false;
    }
  }

  // طلب الأذونات
  Future<void> _requestPermissions() async {
    try {
      // أذونات الأندرويد
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation != null) {
        final bool? granted =
            await androidImplementation.requestNotificationsPermission();
        debugPrint('Android notification permission granted: $granted');

        // طلب أذونات إضافية للأندرويد 13+
        await androidImplementation.requestExactAlarmsPermission();
      }

      // أذونات iOS
      final iosImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation != null) {
        final bool? granted = await iosImplementation.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: true,
        );
        debugPrint('iOS notification permission granted: $granted');
      }
    } catch (e) {
      debugPrint('خطأ في طلب أذونات الإشعارات: $e');
    }
  }

  // تحميل الإعدادات
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _soundEnabled = prefs.getBool('notification_sound') ?? true;
    _vibrationEnabled = prefs.getBool('notification_vibration') ?? true;
  }

  // حفظ الإعدادات
  Future<void> updateSettings({
    bool? soundEnabled,
    bool? vibrationEnabled,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    if (soundEnabled != null) {
      _soundEnabled = soundEnabled;
      await prefs.setBool('notification_sound', soundEnabled);
    }

    if (vibrationEnabled != null) {
      _vibrationEnabled = vibrationEnabled;
      await prefs.setBool('notification_vibration', vibrationEnabled);
    }
  }

  // تحديث أرقام هواتف العملاء (يتم استدعاؤها من SmartNotificationProvider)
  void updateCustomerPhones(Map<String, String> customerPhones) {
    _customerPhones.clear();
    _customerPhones.addAll(customerPhones);
    debugPrint('📱 تم تحديث أرقام هواتف ${customerPhones.length} عميل');
  }

  // إضافة رقم هاتف عميل واحد
  void addCustomerPhone(String customerId, String phoneNumber) {
    if (phoneNumber.isNotEmpty) {
      _customerPhones[customerId] = phoneNumber;
      debugPrint('📱 تم إضافة رقم هاتف للعميل $customerId: $phoneNumber');
    }
  }

  // إضافة معلومات بطاقة عميل
  void addCardInfo(String customerId, Map<String, String> cardInfo) {
    _cardInfoCache[customerId] = cardInfo;
    debugPrint(
      '📋 تم إضافة معلومات البطاقة للعميل $customerId: ${cardInfo['cardType']}',
    );
  }

  // عرض إشعار للديون المتأخرة
  Future<void> showOverdueDebtNotification({
    required String customerName,
    required double amount,
    required int debtCount,
    String? customerId,
  }) async {
    if (!_isInitialized) await initialize();

    // إنشاء معرف فريد لكل إشعار دين متأخر منفصل
    final int notificationId =
        _generateSafeNotificationId('overdue_$customerId');

    // تشغيل الصوت المخصص
    if (_soundEnabled) {
      await _playNotificationSound(NotificationType.overdue);
    }

    // تصميم بطاقة الدين المتأخر
    final String cardTitle = '$customerName - دين متأخر';
    final String cardContent = _buildOverdueDebtCard(
      customerName: customerName,
      amount: amount,
      debtCount: debtCount,
    );

    // إعدادات الأندرويد مع تصميم بطاقة احترافي
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'overdue_debts',
      'الديون المتأخرة',
      channelDescription: 'بطاقات الديون المتأخرة الاحترافية',
      importance: Importance.max,
      priority: Priority.max,
      enableVibration: _vibrationEnabled,
      vibrationPattern:
          _vibrationEnabled ? Int64List.fromList([0, 1000, 500, 1000]) : null,
      color: Colors.red,
      largeIcon: const DrawableResourceAndroidBitmap(
        '@mipmap/launcher_icon',
      ),
      styleInformation: _buildOverdueDebtStyleInformation(
        customerName: customerName,
        amount: amount,
        debtCount: debtCount,
        cardTitle: cardTitle,
        customerId: customerId,
      ),
      ticker: 'دين متأخر - $customerName',
      when: _generateSafeNotificationId('when_overdue'),
      autoCancel: false, // لا يختفي عند النقر
      enableLights: true,
      ledColor: Colors.red,
      ledOnMs: 1000,
      ledOffMs: 500,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      actions: [
        const AndroidNotificationAction(
          'mark_read_overdue',
          '✅ تحديد كمقروء',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        ),
        const AndroidNotificationAction(
          'view_customer',
          '👤 عرض العميل',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'call_customer',
          '📞 اتصال',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
      ],
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'overdue_sound.wav',
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      cardTitle,
      cardContent,
      platformChannelSpecifics,
      payload: '$notificationId:overdue_debt:$customerId:$amount:$debtCount',
    );

    debugPrint(
      '✅ تم عرض بطاقة دين متأخر: $customerName - ${_formatCurrency(amount)}',
    );
  }

  // بناء بطاقة الدين المتأخر الاحترافية
  String _buildOverdueDebtCard({
    required String customerName,
    required double amount,
    required int debtCount,
  }) {
    final String formattedAmount = _formatCurrency(amount);
    final String debtText = debtCount > 1 ? '$debtCount ديون' : 'دين واحد';

    return '''
🔴 دين متأخر

💰 المبلغ: $formattedAmount
📊 العدد: $debtText
⏰ الحالة: متأخر عن السداد

📞 اضغط للاتصال
👁️ اضغط لعرض التفاصيل

⚠️ تذكير: يرجى متابعة هذا الدين''';
  }

  // بناء تصميم إشعار الدين المتأخر مع معلومات كاملة
  InboxStyleInformation _buildOverdueDebtStyleInformation({
    required String customerName,
    required double amount,
    required int debtCount,
    required String cardTitle,
    String? customerId,
  }) {
    final List<String> lines = [];

    // الحصول على معلومات البطاقة الحقيقية أولاً
    final cardInfo =
        customerId != null ? _getDebtCardInfo(customerId) : <String, String>{};

    debugPrint('🔍 معلومات البطاقة للعميل $customerId: $cardInfo');

    // معلومات أساسية
    lines.add('💰 المبلغ: ${_formatCurrency(amount)}');
    lines.add('📊 الكمية: ${cardInfo['quantity'] ?? '1'}');
    lines.add('⏰ الحالة: متأخر عن السداد');

    // معلومات البطاقة التفصيلية - إظهار التواريخ دائماً
    lines.add('');
    lines.add('📋 معلومات البطاقة:');
    lines.add('🏷️ نوع الكارت: ${cardInfo['cardType'] ?? 'غير محدد'}');
    lines.add(
      '📅 تاريخ القيد: ${cardInfo['entryDate'] ?? _formatDateWithDayAndTime(DateTime.now().subtract(const Duration(days: 30)))}',
    );
    lines.add(
      '📅 تاريخ الاستحقاق: ${cardInfo['dueDate'] ?? _formatDateWithDay(DateTime.now().subtract(const Duration(days: 3)))}',
    );
    lines.add('📝 ملاحظات: ${cardInfo['notes'] ?? 'دين متأخر يحتاج متابعة'}');

    lines.add('');
    lines.add('📞 اضغط للاتصال • 👁️ عرض التفاصيل');

    return InboxStyleInformation(
      lines,
      contentTitle: cardTitle,
      summaryText: 'اضغط لعرض التفاصيل والإجراءات',
    );
  }

  // عرض إشعار للديون المستحقة اليوم
  Future<void> showDueTodayNotification({
    required String customerName,
    required double amount,
    required int debtCount,
    String? customerId,
  }) async {
    if (!_isInitialized) await initialize();

    // إنشاء معرف فريد لكل إشعار دين مستحق اليوم منفصل
    final int notificationId =
        _generateSafeNotificationId('due_today_$customerId');

    // تشغيل الصوت المخصص
    if (_soundEnabled) {
      await _playNotificationSound(NotificationType.dueToday);
    }

    // تصميم بطاقة الدين المستحق اليوم
    final String cardTitle = '$customerName - دين مستحق اليوم';
    final String cardContent = _buildDueTodayDebtCard(
      customerName: customerName,
      amount: amount,
      debtCount: debtCount,
    );

    // إعدادات الأندرويد مع تصميم بطاقة احترافي
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'due_today_debts',
      'الديون المستحقة اليوم',
      channelDescription: 'بطاقات الديون المستحقة اليوم الاحترافية',
      importance: Importance.max,
      priority: Priority.max,
      enableVibration: _vibrationEnabled,
      vibrationPattern:
          _vibrationEnabled ? Int64List.fromList([0, 500, 250, 500]) : null,
      color: Colors.orange,
      largeIcon: const DrawableResourceAndroidBitmap(
        '@mipmap/launcher_icon',
      ),
      styleInformation: _buildDueTodayDebtStyleInformation(
        customerName: customerName,
        amount: amount,
        debtCount: debtCount,
        cardTitle: cardTitle,
        customerId: customerId,
      ),
      ticker: 'دين مستحق اليوم - $customerName',
      when: _generateSafeNotificationId('when_due_today'),
      autoCancel: false, // لا يختفي عند النقر
      enableLights: true,
      ledColor: Colors.orange,
      ledOnMs: 800,
      ledOffMs: 400,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      actions: [
        const AndroidNotificationAction(
          'mark_read_today',
          '✅ تحديد كمقروء',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        ),
        const AndroidNotificationAction(
          'view_customer',
          '👤 عرض العميل',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'call_customer',
          '📞 اتصال',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
      ],
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'due_today_sound.wav',
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      cardTitle,
      cardContent,
      platformChannelSpecifics,
      payload: '$notificationId:due_today:$customerId:$amount:$debtCount',
    );

    debugPrint(
      '✅ تم عرض بطاقة دين مستحق اليوم: $customerName - ${_formatCurrency(amount)}',
    );
  }

  // بناء بطاقة الدين المستحق اليوم الاحترافية
  String _buildDueTodayDebtCard({
    required String customerName,
    required double amount,
    required int debtCount,
  }) {
    final String formattedAmount = _formatCurrency(amount);
    final String debtText = debtCount > 1 ? '$debtCount ديون' : 'دين واحد';

    return '''
🔔 دين مستحق اليوم

💰 المبلغ: $formattedAmount
📊 العدد: $debtText
📅 الحالة: مستحق اليوم

📞 اضغط للاتصال
💳 اضغط لتسجيل الدفع
👁️ اضغط لعرض التفاصيل

🔔 تذكير: هذا الدين مستحق اليوم''';
  }

  // بناء تصميم إشعار الدين المستحق اليوم مع معلومات كاملة
  InboxStyleInformation _buildDueTodayDebtStyleInformation({
    required String customerName,
    required double amount,
    required int debtCount,
    required String cardTitle,
    String? customerId,
  }) {
    final List<String> lines = [];

    // الحصول على معلومات البطاقة الحقيقية أولاً
    final cardInfo =
        customerId != null ? _getDebtCardInfo(customerId) : <String, String>{};

    // معلومات أساسية
    lines.add('💰 المبلغ: ${_formatCurrency(amount)}');
    lines.add('📊 الكمية: ${cardInfo['quantity'] ?? '1'}');
    lines.add('📅 الحالة: مستحق اليوم');

    // معلومات البطاقة التفصيلية
    if (customerId != null && cardInfo.isNotEmpty) {
      lines.add('');
      lines.add('📋 معلومات البطاقة:');
      lines.add('🏷️ نوع الكارت: ${cardInfo['cardType'] ?? 'غير محدد'}');
      lines.add(
        '📅 تاريخ القيد: ${cardInfo['entryDate'] ?? _formatDateWithDayAndTime(DateTime.now().subtract(const Duration(days: 30)))}',
      );
      lines.add('📅 تاريخ الاستحقاق: ${_formatDateWithDay(DateTime.now())}');
      lines.add(
        '📝 ملاحظات: ${cardInfo['notes'] ?? 'دين مستحق اليوم - يحتاج متابعة فورية'}',
      );
    }

    lines.add('');
    lines.add('📞 اتصال • 💳 تسجيل دفع • 👁️ التفاصيل');

    return InboxStyleInformation(
      lines,
      contentTitle: cardTitle,
      summaryText: 'تذكير مهم - اضغط للإجراءات',
    );
  }

  // عرض إشعار للديون المستحقة قريباً
  Future<void> showDueSoonNotification({
    required String customerName,
    required double amount,
    required int debtCount,
    required int daysUntilDue,
    String? customerId,
  }) async {
    if (!_isInitialized) await initialize();

    // إنشاء معرف فريد لكل إشعار دين مستحق قريباً منفصل
    final int notificationId =
        _generateSafeNotificationId('due_soon_$customerId');

    // تشغيل الصوت المخصص
    if (_soundEnabled) {
      await _playNotificationSound(NotificationType.dueSoon);
    }

    // تصميم بطاقة الدين المستحق قريباً
    final String cardTitle = '$customerName - دين مستحق قريباً';
    final String cardContent = _buildDueSoonDebtCard(
      customerName: customerName,
      amount: amount,
      debtCount: debtCount,
      daysUntilDue: daysUntilDue,
    );

    // إعدادات الأندرويد
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'due_soon_debts',
      'الديون المستحقة قريباً',
      channelDescription: 'تنبيهات الديون المستحقة قريباً',
      importance: Importance.high,
      priority: Priority.high,
      enableVibration: _vibrationEnabled,
      vibrationPattern: _vibrationEnabled ? Int64List.fromList([0, 300]) : null,
      color: Colors.blue,
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
      styleInformation: _buildDueSoonDebtStyleInformation(
        customerName: customerName,
        amount: amount,
        debtCount: debtCount,
        daysUntilDue: daysUntilDue,
        cardTitle: cardTitle,
        customerId: customerId,
      ),
      ticker: 'ديون مستحقة قريباً',
      when: _generateSafeNotificationId('when_due_soon'),
      autoCancel: false, // لا يختفي عند النقر
      enableLights: true,
      ledColor: Colors.blue,
      ledOnMs: 600,
      ledOffMs: 300,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      actions: [
        const AndroidNotificationAction(
          'mark_read_soon',
          'تحديد كمقروء',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        ),
        const AndroidNotificationAction(
          'view_customer',
          'عرض العميل',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
      ],
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'due_soon_sound.wav',
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      cardTitle,
      cardContent,
      platformChannelSpecifics,
      payload:
          '$notificationId:due_soon:$customerId:$amount:$debtCount:$daysUntilDue',
    );

    debugPrint(
      '✅ تم عرض بطاقة دين مستحق قريباً: $customerName - ${_formatCurrency(amount)} - $daysUntilDue أيام',
    );
  }

  // بناء بطاقة الدين المستحق قريباً الاحترافية
  String _buildDueSoonDebtCard({
    required String customerName,
    required double amount,
    required int debtCount,
    required int daysUntilDue,
  }) {
    final String formattedAmount = _formatCurrency(amount);
    final String debtText = debtCount > 1 ? '$debtCount ديون' : 'دين واحد';
    final String daysText =
        daysUntilDue == 1 ? 'غداً' : 'خلال $daysUntilDue أيام';

    return '''
⏰ دين مستحق قريباً

💰 المبلغ: $formattedAmount
📊 العدد: $debtText
📅 الحالة: مستحق $daysText

📞 اضغط للاتصال
📝 اضغط لإضافة تذكير
👁️ اضغط لعرض التفاصيل

⏰ تذكير: هذا الدين مستحق $daysText''';
  }

  // بناء تصميم إشعار الدين المستحق قريباً مع معلومات كاملة
  InboxStyleInformation _buildDueSoonDebtStyleInformation({
    required String customerName,
    required double amount,
    required int debtCount,
    required int daysUntilDue,
    required String cardTitle,
    String? customerId,
  }) {
    final List<String> lines = [];
    final String daysText =
        daysUntilDue == 1 ? 'غداً' : 'خلال $daysUntilDue أيام';

    // الحصول على معلومات البطاقة الحقيقية أولاً
    final cardInfo =
        customerId != null ? _getDebtCardInfo(customerId) : <String, String>{};

    // معلومات أساسية
    lines.add('💰 المبلغ: ${_formatCurrency(amount)}');
    lines.add('📊 الكمية: ${cardInfo['quantity'] ?? '1'}');
    lines.add('📅 مستحق: $daysText');

    // معلومات البطاقة التفصيلية
    if (customerId != null && cardInfo.isNotEmpty) {
      lines.add('');
      lines.add('📋 تفاصيل البطاقة:');
      lines.add('🏷️ نوع الكارت: ${cardInfo['cardType'] ?? 'غير محدد'}');
      lines.add(
        '📅 تاريخ القيد: ${cardInfo['entryDate'] ?? _formatDateWithDayAndTime(DateTime.now().subtract(const Duration(days: 30)))}',
      );
      lines.add(
        '📅 تاريخ الاستحقاق: ${_formatDateWithDay(DateTime.now().add(Duration(days: daysUntilDue)))}',
      );
      lines.add(
        '📝 ملاحظات: ${cardInfo['notes'] ?? 'دين مستحق قريباً - تذكير مسبق'}',
      );
    }

    lines.add('');
    lines.add('📞 اتصال • 📝 تذكير • 👁️ التفاصيل');

    return InboxStyleInformation(
      lines,
      contentTitle: cardTitle,
      summaryText: 'تذكير مسبق - اضغط للتفاصيل',
    );
  }

  // تشغيل الصوت المخصص
  Future<void> _playNotificationSound(NotificationType type) async {
    try {
      String soundFile;
      switch (type) {
        case NotificationType.overdue:
          soundFile = 'sounds/overdue_alert.mp3';
          break;
        case NotificationType.dueToday:
          soundFile = 'sounds/due_today_alert.mp3';
          break;
        case NotificationType.dueSoon:
          soundFile = 'sounds/due_soon_alert.mp3';
          break;
        default:
          soundFile = 'sounds/default_alert.mp3';
      }

      // محاولة تشغيل الصوت المخصص، وفي حالة الفشل استخدام صوت النظام
      try {
        await _audioPlayer.play(AssetSource(soundFile));
      } catch (assetError) {
        debugPrint('لم يتم العثور على ملف الصوت المخصص: $soundFile');
        // يمكن إضافة صوت النظام الافتراضي هنا إذا لزم الأمر
      }
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت: $e');
    }
  }

  // معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final String? payload = notificationResponse.payload;
    final String? actionId = notificationResponse.actionId;

    debugPrint('تم النقر على الإشعار: $payload, الإجراء: $actionId');

    if (actionId != null) {
      _handleNotificationAction(actionId, payload);
    } else if (payload != null) {
      // النقر العادي على الإشعار
      _handleNotificationTap(payload);
    }
  }

  // معالجة أزرار الإجراءات
  void _handleNotificationAction(String actionId, String? payload) {
    switch (actionId) {
      case 'mark_read_overdue':
      case 'mark_read_today':
      case 'mark_read_soon':
        _markNotificationAsRead(payload);
        break;
      case 'view_customer':
      case 'view_customer_details':
        _openCustomerView(payload);
        break;
      case 'call_customer':
      case 'call_customer_now':
        _callCustomer(payload);
        break;
      case 'mark_as_paid':
        _markDebtAsPaid(payload);
        break;
      case 'mark_all_read':
        _markAllNotificationsAsRead();
        break;
      case 'open_app':
        _openApp(payload);
        break;
      default:
        debugPrint('إجراء غير معروف: $actionId');
    }
  }

  // تحديد الإشعار كمقروء وإزالته
  void _markNotificationAsRead(String? payload) {
    if (payload != null) {
      // استخراج معرف الإشعار من payload
      final parts = payload.split(':');
      if (parts.length >= 2) {
        try {
          final notificationId = int.parse(parts[0]);
          // إزالة الإشعار من لوحة الإشعارات فوراً
          cancelNotification(notificationId);
          debugPrint(
            '✅ تم تحديد الإشعار $notificationId كمقروء وإزالته من الشاشة',
          );

          // إشعار النظام بأن الإشعار تم قراءته
          _notifyNotificationRead(payload);
        } catch (e) {
          debugPrint('خطأ في تحليل معرف الإشعار: $e');
        }
      }
    }
  }

  // إشعار النظام بقراءة الإشعار
  void _notifyNotificationRead(String payload) {
    // يمكن إضافة callback هنا لإشعار التطبيق
    debugPrint('📱 تم إشعار النظام بقراءة الإشعار: $payload');
  }

  // فتح عرض العميل
  void _openCustomerView(String? payload) {
    if (payload != null) {
      debugPrint('فتح عرض العميل: $payload');
      // يمكن إضافة منطق التنقل هنا
      // مثل: Navigator.push(...) أو استخدام callback
    }
  }

  // الاتصال بالعميل
  void _callCustomer(String? payload) {
    if (payload != null) {
      try {
        // استخراج معلومات العميل من payload
        final parts = payload.split(':');
        if (parts.length >= 3) {
          final customerId = parts[2];
          debugPrint('محاولة الاتصال بالعميل: $customerId');

          // البحث عن رقم الهاتف في البيانات المحفوظة
          _findAndCallCustomer(customerId);
        }
      } catch (e) {
        debugPrint('خطأ في الاتصال بالعميل: $e');
      }
    }
  }

  // البحث عن العميل والاتصال به من قاعدة البيانات الحقيقية
  Future<void> _findAndCallCustomer(String customerId) async {
    try {
      debugPrint('🔍 البحث عن رقم هاتف العميل: $customerId');

      // البحث في قاعدة البيانات الحقيقية
      final phoneNumber = await _getCustomerPhoneFromDatabase(customerId);

      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        debugPrint(
          '✅ تم العثور على رقم الهاتف: $phoneNumber للعميل: $customerId',
        );
        await _launchPhoneCall(phoneNumber);
      } else {
        debugPrint('❌ لم يتم العثور على رقم هاتف للعميل: $customerId');
        _showNoPhoneNumberNotification(customerId);
      }
    } catch (e) {
      debugPrint('❌ خطأ في البحث عن العميل: $e');
    }
  }

  // البحث عن رقم هاتف العميل في قاعدة البيانات
  Future<String?> _getCustomerPhoneFromDatabase(String customerId) async {
    try {
      // أولاً: البحث في الخريطة المحدثة من SmartNotificationProvider
      if (_customerPhones.containsKey(customerId)) {
        final phone = _customerPhones[customerId];
        if (phone != null && phone.isNotEmpty) {
          debugPrint('📱 تم العثور على رقم الهاتف من الخريطة: $phone');
          return phone;
        }
      }

      // ثانياً: البحث في SharedPreferences كنسخة احتياطية
      final prefs = await SharedPreferences.getInstance();
      final customerDataJson = prefs.getString('customer_$customerId');

      if (customerDataJson != null) {
        // محاولة استخراج رقم الهاتف من البيانات المحفوظة
        if (customerDataJson.contains('"phone"')) {
          final phoneRegex = RegExp(r'"phone":"([^"]*)"');
          final match = phoneRegex.firstMatch(customerDataJson);
          if (match != null && match.group(1)!.isNotEmpty) {
            final phone = match.group(1)!;
            // حفظ الرقم في الخريطة للمرات القادمة
            _customerPhones[customerId] = phone;
            debugPrint(
              '📱 تم العثور على رقم الهاتف من SharedPreferences: $phone',
            );
            return phone;
          }
        }
      }

      // ثالثاً: البحث في قاعدة البيانات المحلية (SQLite)
      // هذا يتطلب إضافة منطق للوصول إلى قاعدة البيانات مباشرة
      // للآن سنتركه للمستقبل

      debugPrint('❌ لم يتم العثور على رقم هاتف للعميل: $customerId');
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في البحث في قاعدة البيانات: $e');
      return null;
    }
  }

  // عرض إشعار عدم وجود رقم هاتف
  void _showNoPhoneNumberNotification(String customerId) {
    debugPrint('⚠️ لا يوجد رقم هاتف للعميل: $customerId');
    // يمكن إضافة إشعار صغير هنا لإعلام المستخدم
  }

  // تشغيل تطبيق الهاتف للاتصال
  Future<void> _launchPhoneCall(String phoneNumber) async {
    debugPrint('📞 محاولة فتح تطبيق الهاتف للرقم: $phoneNumber');

    try {
      // إنشاء رابط الاتصال
      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

      // التحقق من إمكانية فتح الرابط
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        debugPrint('✅ تم فتح تطبيق الهاتف للاتصال بـ: $phoneNumber');

        // عرض إشعار تأكيد
        _showCallConfirmationNotification(phoneNumber);
      } else {
        debugPrint('❌ لا يمكن فتح تطبيق الهاتف للرقم: $phoneNumber');
        // يمكن إضافة إشعار خطأ هنا
      }
    } catch (e) {
      debugPrint('❌ خطأ في فتح تطبيق الهاتف: $e');
    }
  }

  // عرض إشعار تأكيد الاتصال
  void _showCallConfirmationNotification(String phoneNumber) {
    // يمكن إضافة إشعار صغير يؤكد أن تطبيق الهاتف تم فتحه
    debugPrint('✅ تم فتح تطبيق الهاتف للرقم: $phoneNumber');
  }

  // تحديد الدين كمدفوع
  void _markDebtAsPaid(String? payload) {
    if (payload != null) {
      try {
        final parts = payload.split(':');
        if (parts.length >= 3) {
          final customerId = parts[2];
          debugPrint('تحديد دين العميل كمدفوع: $customerId');

          // هنا يمكن إضافة منطق تحديث قاعدة البيانات
          _updateDebtStatus(customerId);

          // إزالة الإشعار بعد التحديد كمدفوع
          final notificationId = int.tryParse(parts[0]);
          if (notificationId != null) {
            cancelNotification(notificationId);
          }
        }
      } catch (e) {
        debugPrint('خطأ في تحديد الدين كمدفوع: $e');
      }
    }
  }

  // تحديث حالة الدين في قاعدة البيانات
  void _updateDebtStatus(String customerId) {
    // هنا يمكن إضافة منطق تحديث قاعدة البيانات
    debugPrint('🔄 جاري تحديث حالة الدين للعميل: $customerId');

    // عرض إشعار تأكيد
    _showPaymentConfirmationNotification(customerId);
  }

  // عرض إشعار تأكيد الدفع
  void _showPaymentConfirmationNotification(String customerId) {
    debugPrint('✅ تم تحديد دين العميل $customerId كمدفوع');

    // يمكن إضافة إشعار صغير يؤكد التحديث
    // أو إرسال callback للتطبيق الرئيسي
  }

  // تحديد جميع الإشعارات كمقروءة
  void _markAllNotificationsAsRead() {
    // إزالة جميع الإشعارات من لوحة الإشعارات
    cancelAllNotifications();
    debugPrint('✅ تم تحديد جميع الإشعارات كمقروءة وإزالتها من الشاشة');
  }

  // فتح التطبيق
  void _openApp(String? payload) {
    debugPrint('فتح التطبيق: $payload');
    // يمكن إضافة منطق فتح التطبيق هنا
    // مثل: Navigator.pushNamed(...) أو استخدام callback
  }

  // معالجة النقر العادي على الإشعار
  void _handleNotificationTap(String payload) {
    debugPrint('تم النقر على الإشعار: $payload');
    // يمكن إضافة منطق التنقل هنا
  }

  // تنسيق المبلغ مع فاصل الآلاف بدون عملة
  String _formatCurrency(double amount) {
    // تحويل المبلغ إلى نص مع فاصل الآلاف
    final formatter = NumberFormat('#,##0.000', 'ar');
    return formatter.format(amount);
  }

  // إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
  }

  // اختبار الإشعارات
  Future<void> showTestNotification({
    int? id,
    String? title,
    String? body,
  }) async {
    if (!_isInitialized) await initialize();

    final int notificationId = id ?? 9999;
    final String notificationTitle = title ?? 'اختبار الإشعارات 🔔';
    final String notificationBody =
        body ?? 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح';

    // إعدادات الأندرويد
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'test_notifications',
      'إشعارات تجريبية',
      channelDescription: 'إشعارات لاختبار النظام',
      importance: Importance.max,
      priority: Priority.max,
      vibrationPattern: Int64List.fromList([0, 500, 250, 500]),
      color: Colors.green,
      largeIcon: const DrawableResourceAndroidBitmap(
        '@mipmap/launcher_icon',
      ),
      styleInformation: BigTextStyleInformation(
        notificationBody,
        contentTitle: notificationTitle,
        summaryText: 'اختبار الإشعارات',
      ),
      ticker: 'اختبار الإشعارات',
      when: _generateSafeNotificationId('when_test'),
      enableLights: true,
      ledColor: Colors.green,
      ledOnMs: 500,
      ledOffMs: 250,
      category: AndroidNotificationCategory.message,
      visibility: NotificationVisibility.public,
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      notificationTitle,
      notificationBody,
      platformChannelSpecifics,
      payload: 'test_notification',
    );

    debugPrint('✅ تم إرسال إشعار تجريبي');
  }

  // عرض إشعار ملخص للإشعارات المتعددة
  Future<void> showNotificationSummary({
    required int totalCount,
    required int unreadCount,
    required List<String> recentTitles,
    required List<Map<String, dynamic>> overdueCustomers,
    required List<Map<String, dynamic>> dueTodayCustomers,
    required double totalOverdueAmount,
    required double totalDueTodayAmount,
  }) async {
    if (!_isInitialized) await initialize();

    const int notificationId = 1000; // معرف ثابت لإشعار الملخص

    // عنوان احترافي
    final String title = unreadCount > 0
        ? '🔔 لديك $unreadCount إشعار${unreadCount > 1 ? 'ات' : ''} مهم${unreadCount > 1 ? 'ة' : ''}'
        : '✅ جميع الإشعارات مقروءة';

    // محتوى مختصر للعرض السريع
    String body = '';
    if (unreadCount > 0) {
      final List<String> bodyParts = [];

      if (overdueCustomers.isNotEmpty) {
        bodyParts.add(
          '🔴 ${overdueCustomers.length} عميل متأخر (${_formatAmount(totalOverdueAmount)})',
        );
      }

      if (dueTodayCustomers.isNotEmpty) {
        bodyParts.add(
          '🟠 ${dueTodayCustomers.length} عميل مستحق اليوم (${_formatAmount(totalDueTodayAmount)})',
        );
      }

      body = bodyParts.isNotEmpty
          ? bodyParts.join('\n')
          : recentTitles.take(2).join(' • ');
    } else {
      body = 'تم الاطلاع على جميع الإشعارات';
    }

    // إعداد قائمة مفصلة للإشعارات مع تفاصيل كاملة
    final List<String> detailedLines = [];

    if (overdueCustomers.isNotEmpty) {
      detailedLines.add('🔴 عملاء متأخرين (${overdueCustomers.length}):');
      for (int i = 0; i < overdueCustomers.length && i < 3; i++) {
        final customer = overdueCustomers[i];
        final name = customer['name'] ?? '';
        final phone = customer['phone'] ?? '';
        final amount = customer['formattedAmount'] ?? '';
        final debtCount = customer['debtCount'] ?? 1;
        final cardTypes = customer['cardTypes'] ?? '';

        String customerLine = '   • $name';
        if (phone.isNotEmpty) {
          customerLine += ' (📞 $phone)';
        }
        customerLine += ' - $amount';
        if (debtCount > 1) {
          customerLine += ' ($debtCount ديون)';
        }
        if (cardTypes.isNotEmpty) {
          customerLine += ' [$cardTypes]';
        }

        detailedLines.add(customerLine);
      }
      if (overdueCustomers.length > 3) {
        detailedLines.add('   و ${overdueCustomers.length - 3} عملاء آخرين...');
      }
      detailedLines.add(''); // سطر فارغ للفصل
    }

    if (dueTodayCustomers.isNotEmpty) {
      detailedLines.add('🟠 مستحق اليوم (${dueTodayCustomers.length}):');
      for (int i = 0; i < dueTodayCustomers.length && i < 3; i++) {
        final customer = dueTodayCustomers[i];
        final name = customer['name'] ?? '';
        final phone = customer['phone'] ?? '';
        final amount = customer['formattedAmount'] ?? '';
        final debtCount = customer['debtCount'] ?? 1;
        final cardTypes = customer['cardTypes'] ?? '';

        String customerLine = '   • $name';
        if (phone.isNotEmpty) {
          customerLine += ' (📞 $phone)';
        }
        customerLine += ' - $amount';
        if (debtCount > 1) {
          customerLine += ' ($debtCount ديون)';
        }
        if (cardTypes.isNotEmpty) {
          customerLine += ' [$cardTypes]';
        }

        detailedLines.add(customerLine);
      }
      if (dueTodayCustomers.length > 3) {
        detailedLines.add(
          '   و ${dueTodayCustomers.length - 3} عملاء آخرين...',
        );
      }
    }

    // إضافة ملخص المبالغ
    if (totalOverdueAmount > 0 || totalDueTodayAmount > 0) {
      detailedLines.add(''); // سطر فارغ
      detailedLines.add('💰 ملخص المبالغ:');
      if (totalOverdueAmount > 0) {
        detailedLines.add('   🔴 متأخر: ${_formatAmount(totalOverdueAmount)}');
      }
      if (totalDueTodayAmount > 0) {
        detailedLines.add(
          '   🟠 مستحق اليوم: ${_formatAmount(totalDueTodayAmount)}',
        );
      }
      final totalAmount = totalOverdueAmount + totalDueTodayAmount;
      if (totalAmount > 0) {
        detailedLines.add('   📊 الإجمالي: ${_formatAmount(totalAmount)}');
      }
    }

    if (detailedLines.isEmpty && recentTitles.isNotEmpty) {
      detailedLines.addAll(recentTitles.take(5));
    }

    // إعدادات الأندرويد المحسنة
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'notification_summary',
      'ملخص الإشعارات',
      channelDescription: 'ملخص احترافي لجميع الإشعارات المهمة في التطبيق',
      importance: Importance.max,
      priority: Priority.max,
      enableVibration: unreadCount > 0, // اهتزاز فقط عند وجود إشعارات جديدة
      vibrationPattern:
          unreadCount > 0 ? Int64List.fromList([0, 300, 100, 300]) : null,
      color: unreadCount > 0 ? Colors.red : Colors.green,
      largeIcon: const DrawableResourceAndroidBitmap(
        '@mipmap/launcher_icon',
      ),
      styleInformation: _buildSummaryStyleInformation(
        title: title,
        overdueCustomers: overdueCustomers,
        dueTodayCustomers: dueTodayCustomers,
        totalOverdueAmount: totalOverdueAmount,
        totalDueTodayAmount: totalDueTodayAmount,
        totalCount: totalCount,
        unreadCount: unreadCount,
      ),
      ticker: unreadCount > 0 ? 'إشعارات مهمة جديدة' : 'تحديث الإشعارات',
      when: _generateSafeNotificationId('when_summary'),
      enableLights: true,
      ledColor: unreadCount > 0 ? Colors.red : Colors.green,
      ledOnMs: 1000,
      ledOffMs: 500,
      category: unreadCount > 0
          ? AndroidNotificationCategory.reminder
          : AndroidNotificationCategory.status,
      visibility: NotificationVisibility.public,
      groupKey: 'debt_notifications',
      setAsGroupSummary: true,
      autoCancel: false,
      ongoing: unreadCount > 0, // يبقى إذا كان هناك إشعارات غير مقروءة
      actions: [
        if (unreadCount > 0) ...[
          const AndroidNotificationAction(
            'mark_all_read',
            '✓ تحديد الكل كمقروء',
            icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          ),
          const AndroidNotificationAction(
            'open_app',
            '📱 فتح التطبيق',
            icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
            showsUserInterface: true,
          ),
        ] else ...[
          const AndroidNotificationAction(
            'open_app',
            '📱 فتح التطبيق',
            icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
            showsUserInterface: true,
          ),
        ],
      ],
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: false, // بدون صوت للملخص
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      title,
      body,
      platformChannelSpecifics,
      payload: '$notificationId:summary:$totalCount:$unreadCount',
    );

    debugPrint(
      '✅ تم عرض ملخص الإشعارات: $totalCount إجمالي، $unreadCount غير مقروء',
    );
  }

  // إزالة إشعار الملخص
  Future<void> cancelSummaryNotification() async {
    await cancelNotification(1000);
    debugPrint('✅ تم إزالة إشعار الملخص');
  }

  // عرض إشعار مع شريط تقدم (للديون المدفوعة جزئياً)
  Future<void> showProgressNotification({
    required String customerName,
    required double totalAmount,
    required double paidAmount,
    required String customerId,
  }) async {
    if (!_isInitialized) await initialize();

    final int notificationId = 2000 + (customerId.hashCode % 1000);
    final double progressPercentage = (paidAmount / totalAmount * 100).clamp(
      0,
      100,
    );
    final String title = '📊 تقدم سداد العميل $customerName';
    final String body =
        'تم دفع ${_formatAmount(paidAmount)} من أصل ${_formatAmount(totalAmount)} (${progressPercentage.toStringAsFixed(1)}%)';

    // إعدادات الأندرويد مع شريط التقدم
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'payment_progress',
      'تقدم المدفوعات',
      channelDescription: 'عرض تقدم سداد الديون للعملاء',
      importance: Importance.low,
      priority: Priority.low,
      enableVibration: false,
      color: Colors.blue,
      largeIcon: const DrawableResourceAndroidBitmap(
        '@mipmap/launcher_icon',
      ),
      styleInformation: BigTextStyleInformation(
        body,
        contentTitle: title,
        summaryText: 'تحديث تقدم السداد',
      ),
      ticker: 'تحديث تقدم السداد',
      when: _generateSafeNotificationId('when_progress'),
      showProgress: true,
      maxProgress: 100,
      progress: progressPercentage.round(),
      category: AndroidNotificationCategory.progress,
      visibility: NotificationVisibility.public,
      actions: [
        const AndroidNotificationAction(
          'view_customer_details',
          '👤 عرض تفاصيل العميل',
          icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          showsUserInterface: true,
        ),
      ],
    );

    // إعدادات iOS
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: false,
      presentSound: false,
    );

    // الإعدادات النهائية
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      title,
      body,
      platformChannelSpecifics,
      payload:
          '$notificationId:progress:$customerId:${progressPercentage.round()}',
    );

    debugPrint(
      '✅ تم عرض إشعار التقدم: $customerName - ${progressPercentage.toStringAsFixed(1)}%',
    );
  }

  // بناء تصميم إشعار الملخص المحسن
  StyleInformation _buildSummaryStyleInformation({
    required String title,
    required List<Map<String, dynamic>> overdueCustomers,
    required List<Map<String, dynamic>> dueTodayCustomers,
    required double totalOverdueAmount,
    required double totalDueTodayAmount,
    required int totalCount,
    required int unreadCount,
  }) {
    final List<String> lines = [];

    // إضافة معلومات أساسية
    lines.add('📊 إجمالي: $totalCount • 🔔 غير مقروء: $unreadCount');

    if (overdueCustomers.isNotEmpty) {
      lines.add('');
      lines.add('🔴 عملاء متأخرين (${overdueCustomers.length}):');

      // عرض أول 3 عملاء متأخرين
      for (int i = 0; i < overdueCustomers.length && i < 3; i++) {
        final customer = overdueCustomers[i];
        final name = customer['name'] ?? '';
        final amount = customer['formattedAmount'] ?? '';
        final phone = customer['phone'] ?? '';

        String line = '• $name - $amount';
        if (phone.isNotEmpty) {
          line +=
              ' (📞 ${phone.substring(0, phone.length > 7 ? 7 : phone.length)}...)';
        }
        lines.add(line);
      }

      if (overdueCustomers.length > 3) {
        lines.add('و ${overdueCustomers.length - 3} عملاء آخرين...');
      }

      lines.add('💰 إجمالي متأخر: ${_formatAmount(totalOverdueAmount)}');
    }

    if (dueTodayCustomers.isNotEmpty) {
      lines.add('');
      lines.add('🟠 مستحق اليوم (${dueTodayCustomers.length}):');

      // عرض أول 3 عملاء مستحق اليوم
      for (int i = 0; i < dueTodayCustomers.length && i < 3; i++) {
        final customer = dueTodayCustomers[i];
        final name = customer['name'] ?? '';
        final amount = customer['formattedAmount'] ?? '';
        final phone = customer['phone'] ?? '';

        String line = '• $name - $amount';
        if (phone.isNotEmpty) {
          line +=
              ' (📞 ${phone.substring(0, phone.length > 7 ? 7 : phone.length)}...)';
        }
        lines.add(line);
      }

      if (dueTodayCustomers.length > 3) {
        lines.add('و ${dueTodayCustomers.length - 3} عملاء آخرين...');
      }

      lines.add('💰 إجمالي اليوم: ${_formatAmount(totalDueTodayAmount)}');
    }

    if (totalOverdueAmount > 0 || totalDueTodayAmount > 0) {
      lines.add('');
      lines.add(
        '💰 المجموع الكلي: ${_formatAmount(totalOverdueAmount + totalDueTodayAmount)}',
      );
    }

    lines.add('');
    lines.add('📱 اضغط لفتح التطبيق');

    return InboxStyleInformation(
      lines,
      contentTitle: title,
      summaryText:
          'آخر تحديث • ${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
    );
  }

  // الحصول على معلومات البطاقة الحقيقية من الخريطة المحفوظة
  Map<String, String> _getDebtCardInfo(String customerId) {
    // البحث في الخريطة المحفوظة أولاً
    if (_cardInfoCache.containsKey(customerId)) {
      final cachedInfo = _cardInfoCache[customerId]!;
      debugPrint(
        '✅ تم العثور على معلومات البطاقة من الخريطة: ${cachedInfo['cardType']}',
      );
      return cachedInfo;
    }

    // إذا لم نجد معلومات محفوظة، استخدم قيم افتراضية
    final Map<String, String> defaultInfo = {
      'cardType': 'غير محدد',
      'quantity': '1',
      'notes': 'لا توجد ملاحظات',
      'entryDate': _formatDateWithDayAndTime(
        DateTime.now().subtract(const Duration(days: 30)),
      ),
      'dueDate': _formatDateWithDay(
        DateTime.now().subtract(const Duration(days: 3)),
      ),
    };

    debugPrint(
      '⚠️ لم يتم العثور على معلومات البطاقة للعميل $customerId، استخدام قيم افتراضية',
    );
    return defaultInfo;
  }

  // تنسيق التاريخ مع اسم اليوم والوقت
  String _formatDateWithDayAndTime(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    // تحديد صباح أم مساء
    final hour = date.hour;
    final minute = date.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '$dayName ${date.day} $monthName ${date.year} - $displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  // تنسيق التاريخ مع اسم اليوم فقط (للاستحقاق)
  String _formatDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final dayName = dayNames[date.weekday % 7];
    final monthName = monthNames[date.month - 1];

    return '$dayName ${date.day} $monthName ${date.year}';
  }

  // تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return '${amount.toStringAsFixed(0)} ر.س';
    }
  }

  // جدولة تنبيه في وقت محدد - نسخة مبسطة وآمنة
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
  }) async {
    try {
      // تهيئة timezone إذا لم تكن مهيأة
      await _initializeTimezone();

      // التأكد من أن الوقت في المستقبل
      final now = DateTime.now();
      var finalScheduledTime = scheduledTime;

      if (finalScheduledTime.isBefore(now)) {
        finalScheduledTime = finalScheduledTime.add(const Duration(days: 1));
      }

      // تحويل إلى TZDateTime بطريقة آمنة
      tz.TZDateTime tzScheduledTime;
      try {
        tzScheduledTime = tz.TZDateTime.from(finalScheduledTime, tz.local);
      } catch (e) {
        // في حالة فشل التحويل، استخدم UTC
        debugPrint(
            '⚠️ فشل في استخدام المنطقة الزمنية المحلية، استخدام UTC: $e');
        tzScheduledTime = tz.TZDateTime.from(finalScheduledTime, tz.UTC);
      }

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'scheduled_channel',
            'التنبيهات المجدولة',
            channelDescription: 'تنبيهات في أوقات محددة',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // يتكرر يومياً
      );

      debugPrint('✅ تم جدولة التنبيه $id في $finalScheduledTime');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة التنبيه: $e');
      // محاولة جدولة بسيطة كبديل
      await _fallbackScheduleNotification(id, title, body, scheduledTime);
    }
  }

  // جدولة تنبيه أسبوعي ليوم محدد
  Future<void> scheduleWeeklyNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    required int weekday,
  }) async {
    try {
      // تهيئة timezone إذا لم تكن مهيأة
      await _initializeTimezone();

      // تحويل إلى TZDateTime بطريقة آمنة
      tz.TZDateTime tzScheduledTime;
      try {
        tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);
      } catch (e) {
        // في حالة فشل التحويل، استخدم UTC
        debugPrint(
            '⚠️ فشل في استخدام المنطقة الزمنية المحلية، استخدام UTC: $e');
        tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.UTC);
      }

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'weekly_scheduled_channel',
            'التنبيهات الأسبوعية المجدولة',
            channelDescription: 'تنبيهات أسبوعية في أوقات وأيام محددة',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents
            .dayOfWeekAndTime, // يتكرر أسبوعياً في نفس اليوم والوقت
      );

      debugPrint(
          '✅ تم جدولة التنبيه الأسبوعي $id في $scheduledTime ليوم $weekday');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة التنبيه الأسبوعي: $e');
      // محاولة جدولة بسيطة كبديل
      await _fallbackScheduleNotification(id, title, body, scheduledTime);
    }
  }

  // دالة مساعدة لتهيئة timezone
  Future<void> _initializeTimezone() async {
    try {
      if (tz.local.name == 'UTC') {
        // محاولة تحديد المنطقة الزمنية
        tz.setLocalLocation(tz.getLocation('Asia/Riyadh'));
      }
    } catch (e) {
      debugPrint('⚠️ تحذير: لم يتم تحديد المنطقة الزمنية، استخدام UTC: $e');
    }
  }

  // دالة بديلة للجدولة في حالة فشل الطريقة الأساسية
  Future<void> _fallbackScheduleNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
  ) async {
    try {
      // عرض إشعار فوري كبديل
      await _flutterLocalNotificationsPlugin.show(
        id,
        '⏰ تنبيه مجدول: $title',
        '$body\n(كان مجدولاً لـ ${scheduledTime.hour}:${scheduledTime.minute.toString().padLeft(2, '0')})',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'fallback_channel',
            'التنبيهات البديلة',
            channelDescription: 'تنبيهات بديلة عند فشل الجدولة',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
      debugPrint('✅ تم عرض تنبيه بديل للمعرف $id');
    } catch (e) {
      debugPrint('❌ فشل في عرض التنبيه البديل: $e');
    }
  }

  // جدولة تنبيه الديون مع البيانات الفعلية والصوت
  Future<void> scheduleDebtNotification({
    required int id,
    required String title,
    required DateTime scheduledTime,
    required String notificationType,
  }) async {
    try {
      // الحصول على الديون الفعلية
      final debtData = await _getActualDebtsData();

      String notificationTitle;
      String notificationBody;

      // طباعة تفاصيل البيانات للتشخيص
      debugPrint('🔍 تشخيص بيانات الديون:');
      debugPrint('📊 إجمالي الديون: ${debtData['totalDebts']}');
      debugPrint('💰 إجمالي المبلغ: ${debtData['totalAmount']}');
      debugPrint('🔴 ديون متأخرة: ${debtData['overdueDebts']}');
      debugPrint('🟠 ديون مستحقة اليوم: ${debtData['dueTodayDebts']}');
      debugPrint('👥 أسماء العملاء: ${debtData['customerNames']}');
      debugPrint('✅ هل توجد ديون: ${debtData['hasDebts']}');

      final totalDebts = debtData['totalDebts'] ?? 0;
      final overdueDebts = debtData['overdueDebts'] ?? 0;
      final dueTodayDebts = debtData['dueTodayDebts'] ?? 0;

      // التحقق من وجود ديون مهمة (متأخرة أو مستحقة اليوم أو أي ديون)
      final hasImportantDebts =
          totalDebts > 0 || overdueDebts > 0 || dueTodayDebts > 0;

      if (hasImportantDebts) {
        notificationTitle = '🔔 $title - لديك ديون مهمة!';
        notificationBody = _buildDebtNotificationBody(
          debtData,
          notificationType,
        );
        debugPrint('✅ سيتم عرض تنبيه الديون المهمة');
      } else {
        notificationTitle = '✅ $title - لا توجد ديون مستحقة';
        notificationBody = 'جميع الديون محدثة. استمر في العمل الرائع!';
        debugPrint('ℹ️ سيتم عرض تنبيه عدم وجود ديون');
      }

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        notificationTitle,
        notificationBody,
        tz.TZDateTime.from(scheduledTime, tz.local),
        NotificationDetails(
          android: AndroidNotificationDetails(
            'debt_scheduled_channel',
            'تنبيهات الديون المجدولة',
            channelDescription: 'تنبيهات الديون في أوقات محددة مع صوت',
            importance: Importance.max,
            priority: Priority.max,
            icon: '@mipmap/ic_launcher',
            largeIcon: const DrawableResourceAndroidBitmap(
              '@mipmap/launcher_icon',
            ),
            styleInformation: BigTextStyleInformation(
              notificationBody,
              contentTitle: notificationTitle,
              summaryText: 'تنبيه ديون مجدول',
            ),
            category: AndroidNotificationCategory.reminder,
            visibility: NotificationVisibility.public,
            enableLights: true,
            ledColor: debtData['hasDebts'] ? Colors.red : Colors.green,
            ledOnMs: 1000,
            ledOffMs: 500,
            actions: [
              const AndroidNotificationAction(
                'view_debts',
                '👁️ عرض الديون',
                icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
                showsUserInterface: true,
              ),
              const AndroidNotificationAction(
                'mark_read',
                '✓ تم الاطلاع',
                icon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
              ),
            ],
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // يتكرر يومياً
      );

      // تشغيل الصوت المخصص
      if (_soundEnabled && debtData['hasDebts']) {
        await _playNotificationSound(NotificationType.overdue);
      }

      debugPrint('✅ تم جدولة تنبيه الديون $id في $scheduledTime');
      debugPrint('📊 عدد الديون: ${debtData['totalDebts']}');
      debugPrint('💰 إجمالي المبلغ: ${debtData['totalAmount']}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة تنبيه الديون: $e');
    }
  }

  // الحصول على بيانات الديون الفعلية من قاعدة البيانات
  Future<Map<String, dynamic>> _getActualDebtsData() async {
    try {
      // استخدام DatabaseHelper مباشرة للحصول على البيانات الفعلية
      final databaseHelper = DatabaseHelper();

      // تحميل جميع الديون والعملاء
      final debts = await databaseHelper.getAllDebts();
      final customers = await databaseHelper.getAllCustomers();

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // فلترة الديون غير المسددة
      final unpaidDebts =
          debts.where((debt) => debt.remainingAmount > 0).toList();

      final int totalDebts = unpaidDebts.length;
      final double totalAmount = unpaidDebts.fold(
        0.0,
        (sum, debt) => sum + debt.remainingAmount,
      );
      int overdueDebts = 0;
      int dueTodayDebts = 0;
      final Set<String> customerNamesSet = {};

      for (final debt in unpaidDebts) {
        // العثور على العميل
        final customer = customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => Customer(
            name: 'عميل غير معروف',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        customerNamesSet.add(customer.name);

        // فحص تاريخ الاستحقاق
        final dueDate = DateTime(
          debt.dueDate.year,
          debt.dueDate.month,
          debt.dueDate.day,
        );

        if (dueDate.isBefore(today)) {
          overdueDebts++;
        } else if (dueDate.isAtSameMomentAs(today)) {
          dueTodayDebts++;
        }
      }

      final customerNames = customerNamesSet.toList();

      debugPrint('📊 النتائج النهائية من قاعدة البيانات:');
      debugPrint('   إجمالي الديون: $totalDebts');
      debugPrint('   إجمالي المبلغ: $totalAmount');
      debugPrint('   ديون متأخرة: $overdueDebts');
      debugPrint('   ديون مستحقة اليوم: $dueTodayDebts');
      debugPrint('   عدد العملاء: ${customerNames.length}');
      debugPrint('   أسماء العملاء: ${customerNames.join(", ")}');

      return {
        'hasDebts': totalDebts > 0,
        'totalDebts': totalDebts,
        'totalAmount': totalAmount,
        'overdueDebts': overdueDebts,
        'dueTodayDebts': dueTodayDebts,
        'customerNames': customerNames,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على بيانات الديون من قاعدة البيانات: $e');
      return {
        'hasDebts': false,
        'totalDebts': 0,
        'totalAmount': 0.0,
        'overdueDebts': 0,
        'dueTodayDebts': 0,
        'customerNames': <String>[],
      };
    }
  }

  // بناء محتوى تنبيه الديون
  String _buildDebtNotificationBody(
    Map<String, dynamic> debtData,
    String notificationType,
  ) {
    final totalDebts = debtData['totalDebts'] ?? 0;
    final totalAmount = debtData['totalAmount'] ?? 0.0;
    final overdueDebts = debtData['overdueDebts'] ?? 0;
    final dueTodayDebts = debtData['dueTodayDebts'] ?? 0;
    final customerNames = debtData['customerNames'] as List<String>? ?? [];

    String body = '';

    // إضافة معلومات الديون المهمة أولاً
    if (overdueDebts > 0) {
      body += '🔴 ديون متأخرة: $overdueDebts\n';
    }

    if (dueTodayDebts > 0) {
      body += '🟠 ديون مستحقة اليوم: $dueTodayDebts\n';
    }

    // إضافة الإجماليات
    if (totalDebts > 0) {
      body += '📊 إجمالي الديون: $totalDebts\n';
      body += '💰 إجمالي المبلغ: ${_formatCurrency(totalAmount)}\n';
    }

    // إضافة أسماء العملاء
    if (customerNames.isNotEmpty) {
      body += '\n👥 العملاء: ';
      if (customerNames.length <= 3) {
        body += customerNames.join(', ');
      } else {
        body +=
            '${customerNames.take(3).join(', ')} و ${customerNames.length - 3} آخرين';
      }
    }

    // إضافة رسالة الإجراء
    body += '\n\n📱 اضغط لعرض التفاصيل';

    // طباعة المحتوى للتشخيص
    debugPrint('📝 محتوى التنبيه المُنشأ:');
    debugPrint(body);

    return body;
  }

  // دالة عامة للتشخيص
  Future<Map<String, dynamic>> diagnoseDebtsData() async {
    return _getActualDebtsData();
  }

  // اختبار التنبيهات - دالة مبسطة للاختبار
  Future<void> testNotification() async {
    try {
      await _flutterLocalNotificationsPlugin.show(
        999999, // معرف اختبار
        '🧪 اختبار التنبيهات',
        'هذا تنبيه اختبار للتأكد من عمل النظام بشكل صحيح',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'test_channel',
            'اختبار التنبيهات',
            channelDescription: 'قناة اختبار التنبيهات',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
      debugPrint('✅ تم عرض تنبيه الاختبار بنجاح');
    } catch (e) {
      debugPrint('❌ فشل في عرض تنبيه الاختبار: $e');
    }
  }

  // تنظيف الموارد
  void dispose() {
    _audioPlayer.dispose();
  }
}
