import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../utils/number_formatter.dart';

class PaymentStatisticsScreen extends StatelessWidget {
  const PaymentStatisticsScreen({super.key, required this.customer});
  final Customer customer;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إحصائيات التسديدات - ${customer.name}',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          // Get paid debts for this customer
          final paidDebts = debtProvider.debts
              .where((debt) => debt.status == DebtStatus.paid)
              .toList();

          if (paidDebts.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد تسديدات بعد',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ستظهر الإحصائيات هنا عند إجراء تسديدات',
                    style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  ),
                ],
              ),
            );
          }

          // Calculate statistics
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);
          final yesterday = today.subtract(const Duration(days: 1));
          final weekStart = today.subtract(Duration(days: now.weekday - 1));
          final monthStart = DateTime(now.year, now.month);

          final todayPayments = paidDebts.where((debt) {
            final paymentDate = DateTime(
              debt.entryDate.year,
              debt.entryDate.month,
              debt.entryDate.day,
            );
            return paymentDate.isAtSameMomentAs(today);
          }).toList();

          final yesterdayPayments = paidDebts.where((debt) {
            final paymentDate = DateTime(
              debt.entryDate.year,
              debt.entryDate.month,
              debt.entryDate.day,
            );
            return paymentDate.isAtSameMomentAs(yesterday);
          }).toList();

          final weekPayments = paidDebts.where((debt) {
            final paymentDate = debt.entryDate;
            return paymentDate.isAfter(
              weekStart.subtract(const Duration(days: 1)),
            );
          }).toList();

          final monthPayments = paidDebts.where((debt) {
            final paymentDate = debt.entryDate;
            return paymentDate.isAfter(
              monthStart.subtract(const Duration(days: 1)),
            );
          }).toList();

          // Get the latest payment
          final allPayments = debtProvider.payments;
          final latestPayment = allPayments.isNotEmpty
              ? allPayments.reduce(
                  (a, b) => a.paymentDate.isAfter(b.paymentDate) ? a : b,
                )
              : null;

          // Find the debt associated with the latest payment
          final latestDebt = latestPayment != null
              ? () {
                  final debts = paidDebts.where(
                    (debt) => debt.id == latestPayment.debtId,
                  );
                  return debts.isNotEmpty ? debts.first : null;
                }()
              : null;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Latest Payment Section
                if (latestPayment != null && latestDebt != null) ...[
                  _buildLatestPaymentCard(context, latestPayment, latestDebt),
                  const SizedBox(height: 20),
                ],

                // Statistics Grid Title
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade50, Colors.purple.shade50],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.bar_chart, color: Colors.blue[700], size: 28),
                      const SizedBox(width: 16),
                      Text(
                        'إحصائيات مفصلة',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Statistics Grid
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Wrap(
                      spacing: 16,
                      runSpacing: 16,
                      children: [
                        // Today Payments
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCardWithAmount(
                            'تسديد اليوم',
                            '${todayPayments.length}',
                            _getTotalAmount(todayPayments),
                            Icons.today,
                            Colors.green,
                            'كارت',
                          ),
                        ),

                        // Yesterday Payments
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCardWithAmount(
                            'تسديد أمس',
                            '${yesterdayPayments.length}',
                            _getTotalAmount(yesterdayPayments),
                            Icons.history,
                            Colors.blue,
                            'كارت',
                          ),
                        ),

                        // Week Payments
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCardWithAmount(
                            'تسديد الأسبوع',
                            '${weekPayments.length}',
                            _getTotalAmount(weekPayments),
                            Icons.date_range,
                            Colors.orange,
                            'كارت',
                          ),
                        ),

                        // Month Payments
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCardWithAmount(
                            'تسديد الشهر',
                            '${monthPayments.length}',
                            _getTotalAmount(monthPayments),
                            Icons.calendar_month,
                            Colors.purple,
                            'كارت',
                          ),
                        ),

                        // Total Cards
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCardWithAmount(
                            'إجمالي الكارتات المسددة',
                            '${paidDebts.length}',
                            _getTotalAmount(paidDebts),
                            Icons.check_circle,
                            Colors.teal,
                            'كارت',
                          ),
                        ),

                        // Total Amount
                        SizedBox(
                          width: (constraints.maxWidth - 16) / 2,
                          child: _buildStatCard(
                            'إجمالي المبالغ المسددة',
                            NumberFormatter.formatNumber(
                              _getTotalAmount(paidDebts),
                            ),
                            Icons.account_balance_wallet,
                            Colors.indigo,
                            'د.ع',
                          ),
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }

  double _getTotalAmount(List<Debt> debts) {
    return debts.fold(0.0, (sum, debt) => sum + debt.amount);
  }

  Widget _buildStatCardWithAmount(
    String label,
    String count,
    double amount,
    IconData icon,
    Color color,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon and Count Section
          Column(
            children: [
              // 3D Icon
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [color, color.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Icon(icon, size: 28, color: Colors.white),
              ),

              const SizedBox(height: 12),

              // Count
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Flexible(
                    child: Text(
                      count,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color:
                            Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    unit,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Label and Amount Section
          Column(
            children: [
              // Label
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // Amount
              Text(
                NumberFormatter.formatCurrency(amount),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon and Value Section
          Column(
            children: [
              // 3D Icon
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [color, color.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Icon(icon, size: 28, color: Colors.white),
              ),

              const SizedBox(height: 12),

              // Value
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Flexible(
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color:
                            Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    unit,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Label Section
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildLatestPaymentCard(
    BuildContext context,
    dynamic latestPayment,
    Debt latestDebt,
  ) {
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );
    final cardTypeOption = cardTypeProvider.getCardTypeById(
      latestDebt.cardType,
    );
    final cardTypeName = cardTypeOption?.displayName ?? latestDebt.cardType;

    final now = DateTime.now();
    final paymentDate = latestPayment.paymentDate;
    final daysDifference = now.difference(paymentDate).inDays;

    String timeAgo;
    if (daysDifference == 0) {
      timeAgo = 'اليوم';
    } else if (daysDifference == 1) {
      timeAgo = 'أمس';
    } else {
      timeAgo = 'منذ $daysDifference أيام';
    }

    final dayName = _getDayName(paymentDate.weekday);
    final timeOfDay = _getTimeOfDay(paymentDate.hour);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue, Colors.blue.shade600],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(Icons.payment, color: Colors.white, size: 28),
              ),
              const SizedBox(width: 20),
              const Expanded(
                child: Text(
                  'آخر عملية تسديد',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  timeAgo,
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Payment Details
          Row(
            children: [
              // Left Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                      Icons.person,
                      'اسم العميل',
                      customer.name,
                      Colors.blue,
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow(
                      Icons.credit_card,
                      'نوع الكارت',
                      cardTypeName,
                      Colors.purple,
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow(
                      Icons.numbers,
                      'الكمية',
                      NumberFormatter.formatNumber(latestDebt.quantity),
                      Colors.orange,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Right Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                      Icons.attach_money,
                      'المبلغ',
                      NumberFormatter.formatCurrency(latestPayment.amount),
                      Colors.green,
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow(
                      Icons.calendar_today,
                      'التاريخ',
                      '${paymentDate.day}/${paymentDate.month}/${paymentDate.year}',
                      Colors.teal,
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow(
                      Icons.schedule,
                      'اليوم والوقت',
                      '$dayName ${paymentDate.hour.toString().padLeft(2, '0')}:${paymentDate.minute.toString().padLeft(2, '0')} $timeOfDay',
                      Colors.indigo,
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Notes (if available)
          if (latestPayment.notes != null &&
              latestPayment.notes!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.note_alt, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الملاحظة:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          latestPayment.notes!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, size: 20, color: color),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getDayName(int weekday) {
    const days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return days[weekday - 1];
  }

  String _getTimeOfDay(int hour) {
    if (hour >= 5 && hour < 12) {
      return 'صباحاً';
    } else if (hour >= 12 && hour < 17) {
      return 'ظهراً';
    } else if (hour >= 17 && hour < 20) {
      return 'عصراً';
    } else if (hour >= 20 && hour < 24) {
      return 'مساءً';
    } else {
      return 'فجراً';
    }
  }
}
