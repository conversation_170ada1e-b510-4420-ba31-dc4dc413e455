import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/customer.dart';
import '../providers/debt_provider.dart';
import 'payment_card.dart';

// أنواع عرض التسديدات
enum PaymentCardViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  detailed, // عرض مفصل
  grid, // عرض شبكي
  timeline, // عرض زمني
}

class PaymentsTab extends StatefulWidget {
  const PaymentsTab({
    super.key,
    required this.customer,
    this.isSelectionMode = false,
    this.selectedPaymentIds = const {},
    this.onSelectionChanged,
  });
  final Customer customer;
  final bool isSelectionMode;
  final Set<int> selectedPaymentIds;
  final Function(int paymentId, bool isSelected)? onSelectionChanged;

  @override
  State<PaymentsTab> createState() => _PaymentsTabState();
}

class _PaymentsTabState extends State<PaymentsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};

  @override
  void initState() {
    super.initState();

    // Load payments when tab is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload payments when dependencies change (e.g., when returning from payment dialog)
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.loadCustomerPayments(widget.customer.id!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Get all payments for this customer
        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        debugPrint(
          'PaymentsTab: Total payments in provider: ${debtProvider.payments.length}',
        );
        debugPrint(
          'PaymentsTab: Customer ${widget.customer.id} payments: ${customerPayments.length}',
        );

        if (customerPayments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.payment_outlined, size: 80, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد تسديدات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر التسديدات هنا بعد إجراء عمليات الدفع',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // شريط العرض (فقط عندما لا نكون في وضع التحديد)
            if (!_isSelectionMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // عنوان القسم
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.payment,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'قائمة التسديدات (${customerPayments.length})',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

            // Action buttons
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Top row - Selection and Statistics buttons
                  if (customerPayments.isNotEmpty) ...[
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _isSelectionMode
                          ? Row(
                              key: const ValueKey('selection_mode'),
                              children: [
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () => _toggleSelectionMode(),
                                    icon: const Icon(Icons.close, size: 18),
                                    label: const Text(
                                      'إلغاء التحديد',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.red,
                                      side: BorderSide(
                                        color: Colors.red.shade300,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _selectedPaymentIds.isEmpty
                                        ? null
                                        : () => _deleteSelectedPayments(),
                                    icon: const Icon(Icons.delete, size: 18),
                                    label: Text(
                                      'حذف المحدد (${_selectedPaymentIds.length})',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          _selectedPaymentIds.isEmpty
                                          ? Colors.grey[400]
                                          : Colors.red[600],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _deleteAllPayments(),
                                    icon: const Icon(
                                      Icons.delete_forever,
                                      size: 18,
                                    ),
                                    label: const Text(
                                      'حذف الكل',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red[700],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    const SizedBox(height: 8),
                  ],
                ],
              ),
            ),

            // Payments List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerPayments(widget.customer.id!);
                },
                child: _buildPaymentsView(customerPayments, debtProvider),
              ),
            ),
          ],
        );
      },
    );
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void _deleteSelectedPayments() {
    if (_selectedPaymentIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.delete, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف التسديدات المحددة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف ${_selectedPaymentIds.length} تسديد محدد نهائياً؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );

                final selectedCount = _selectedPaymentIds.length;
                for (final paymentId in _selectedPaymentIds) {
                  await debtProvider.deletePayment(paymentId);
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف $selectedCount تسديد نهائياً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائياً',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (customerPayments.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف جميع التسديدات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف جميع التسديدات (${customerPayments.length} تسديد) نهائياً؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final totalCount = customerPayments.length;
                for (final payment in customerPayments) {
                  if (payment.id != null) {
                    await debtProvider.deletePayment(payment.id!);
                  }
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف $totalCount تسديد نهائياً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائياً',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء عرض التسديدات (العرض العادي فقط)
  Widget _buildPaymentsView(List customerPayments, debtProvider) {
    return _buildStandardPaymentsView(customerPayments, debtProvider);
  }

  // العرض العادي (استخدام PaymentCard الأصلي)
  Widget _buildStandardPaymentsView(List customerPayments, debtProvider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      itemCount: customerPayments.length,
      itemBuilder: (context, index) {
        final payment = customerPayments[index];
        // Find the debt for this payment
        final debts = debtProvider.debts.where((d) => d.id == payment.debtId);
        final debt = debts.isNotEmpty ? debts.first : null;

        return GestureDetector(
          onTap: widget.isSelectionMode
              ? () {
                  if (payment.id != null && widget.onSelectionChanged != null) {
                    final isSelected = widget.selectedPaymentIds.contains(
                      payment.id!,
                    );
                    widget.onSelectionChanged!(payment.id!, !isSelected);
                  }
                }
              : null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration:
                widget.isSelectionMode &&
                    payment.id != null &&
                    widget.selectedPaymentIds.contains(payment.id!)
                ? BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF1976D2),
                      width: 3,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF1976D2).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  )
                : widget.isSelectionMode
                ? BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                  )
                : null,
            child: Stack(
              children: [
                PaymentCard(
                  payment: payment,
                  debt: debt,
                  customer: widget.customer,
                ),
                if (widget.isSelectionMode && payment.id != null)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: widget.selectedPaymentIds.contains(payment.id!)
                            ? const Color(0xFF1976D2)
                            : Colors.white,
                        border: Border.all(
                          color: widget.selectedPaymentIds.contains(payment.id!)
                              ? const Color(0xFF1976D2)
                              : Colors.grey.withValues(alpha: 0.5),
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: widget.selectedPaymentIds.contains(payment.id!)
                          ? const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.white,
                            )
                          : null,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
