@echo off
echo ========================================
echo    اختبار تطبيق محاسب الديون الاحترافي
echo ========================================
echo.

echo 1. فحص بيئة Flutter...
echo ----------------------------------------
flutter --version
echo.

echo 2. فحص الأجهزة المتاحة...
echo ----------------------------------------
flutter devices
echo.

echo 3. تنظيف المشروع...
echo ----------------------------------------
flutter clean
echo.

echo 4. تحديث التبعيات...
echo ----------------------------------------
flutter pub get
echo.

echo 5. اختبار التطبيق البسيط...
echo ----------------------------------------
echo تشغيل test_app.dart...
timeout /t 3 >nul
flutter run test_app.dart --debug
echo.

echo 6. اختبار قاعدة البيانات...
echo ----------------------------------------
echo تشغيل test_database.dart...
timeout /t 3 >nul
flutter run test_database.dart --debug
echo.

echo 7. اختبار التطبيق مع التشخيص...
echo ----------------------------------------
echo تشغيل debug_main.dart...
timeout /t 3 >nul
flutter run debug_main.dart --debug
echo.

echo 8. تشغيل التطبيق الأصلي...
echo ----------------------------------------
echo تشغيل lib/main.dart...
timeout /t 3 >nul
flutter run lib/main.dart --debug
echo.

echo ========================================
echo           انتهى الاختبار
echo ========================================
echo.
echo إذا واجهت مشاكل، راجع ملف TROUBLESHOOTING_GUIDE.md
echo.
pause
