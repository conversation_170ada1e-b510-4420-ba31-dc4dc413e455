#!/usr/bin/env python3
"""
إنشاء أيقونة التطبيق الجديدة
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    # إنشاء صورة بدقة 512x512
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية دائرية متدرجة
    # خلفية داكنة
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color1 = (15, 32, 39, alpha)  # #0F2027
        color2 = (32, 58, 67, alpha)  # #203A43
        color3 = (44, 83, 100, alpha) # #2C5364
        
        # رسم دوائر متدرجة
        draw.ellipse([i, i, size-i, size-i], fill=color3)
    
    # دائرة خارجية مع تدرج أزرق-بنفسجي
    outer_radius = 200
    center = size // 2
    for i in range(20):
        alpha = int(255 * (1 - i / 20))
        r = int(102 + (118-102) * i / 20)  # من #667eea إلى #764ba2
        g = int(126 + (75-126) * i / 20)
        b = int(234 + (162-234) * i / 20)
        color = (r, g, b, alpha)
        
        radius = outer_radius - i * 2
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], fill=color)
    
    # دائرة بيضاء داخلية
    inner_radius = 160
    draw.ellipse([center-inner_radius, center-inner_radius, center+inner_radius, center+inner_radius], fill=(255, 255, 255, 255))
    
    # أيقونة الحاسبة
    calc_size = 120
    calc_x = center - calc_size // 2
    calc_y = center - calc_size // 2
    
    # خلفية الحاسبة
    draw.rounded_rectangle([calc_x, calc_y, calc_x + calc_size, calc_y + calc_size], radius=25, fill=(255, 255, 255, 255))
    
    # أيقونة الحاسبة الداخلية
    inner_calc_size = 80
    inner_calc_x = center - inner_calc_size // 2
    inner_calc_y = center - inner_calc_size // 2
    
    # تدرج للحاسبة
    for i in range(inner_calc_size):
        alpha = 255
        r = int(102 + (118-102) * i / inner_calc_size)
        g = int(126 + (75-126) * i / inner_calc_size)
        b = int(234 + (162-234) * i / inner_calc_size)
        color = (r, g, b, alpha)
        
        draw.rounded_rectangle([inner_calc_x, inner_calc_y + i, inner_calc_x + inner_calc_size, inner_calc_y + i + 1], radius=15, fill=color)
    
    # شاشة الحاسبة
    screen_width = 60
    screen_height = 20
    screen_x = center - screen_width // 2
    screen_y = center - 30
    draw.rounded_rectangle([screen_x, screen_y, screen_x + screen_width, screen_y + screen_height], radius=4, fill=(44, 83, 100, 255))
    
    # أزرار الحاسبة
    button_size = 12
    button_spacing = 15
    start_x = center - (3 * button_size + 2 * button_spacing) // 2
    start_y = center + 10
    
    for row in range(3):
        for col in range(3):
            x = start_x + col * button_spacing
            y = start_y + row * button_spacing
            draw.rounded_rectangle([x, y, x + button_size, y + button_size], radius=2, fill=(255, 255, 255, 255))
    
    # عملة ذهبية
    coin_radius = 30
    coin_x = center + 120
    coin_y = center - 120
    
    # تدرج ذهبي
    for i in range(coin_radius):
        alpha = int(255 * (1 - i / coin_radius))
        r = int(255 + (255-255) * i / coin_radius)  # من #FFD700 إلى #FFA500
        g = int(215 + (165-215) * i / coin_radius)
        b = int(0 + (0-0) * i / coin_radius)
        color = (r, g, b, alpha)
        
        draw.ellipse([coin_x-coin_radius+i, coin_y-coin_radius+i, coin_x+coin_radius-i, coin_y+coin_radius-i], fill=color)
    
    # حدود بيضاء للعملة
    draw.ellipse([coin_x-coin_radius-3, coin_y-coin_radius-3, coin_x+coin_radius+3, coin_y+coin_radius+3], outline=(255, 255, 255, 255), width=3)
    
    # بطاقة ائتمان
    card_width = 50
    card_height = 32
    card_x = center - 150
    card_y = center + 100
    
    # تدرج أخضر
    for i in range(card_height):
        alpha = 255
        r = int(76 + (69-76) * i / card_height)   # من #4CAF50 إلى #45a049
        g = int(175 + (160-175) * i / card_height)
        b = int(80 + (73-80) * i / card_height)
        color = (r, g, b, alpha)
        
        draw.rounded_rectangle([card_x, card_y + i, card_x + card_width, card_y + i + 1], radius=6, fill=color)
    
    # حدود بيضاء للبطاقة
    draw.rounded_rectangle([card_x-3, card_y-3, card_x + card_width+3, card_y + card_height+3], radius=6, outline=(255, 255, 255, 255), width=3)
    
    # خط مغناطيسي على البطاقة
    draw.rounded_rectangle([card_x + 6, card_y + 10, card_x + card_width - 6, card_y + 14], radius=1, fill=(255, 255, 255, 200))
    
    # نقاط البطاقة
    for i in range(4):
        dot_x = card_x + 8 + i * 6
        dot_y = card_y + 22
        draw.ellipse([dot_x-2, dot_y-2, dot_x+2, dot_y+2], fill=(255, 255, 255, 255))
    
    return img

def main():
    print("🎨 إنشاء أيقونة التطبيق الجديدة...")
    
    # إنشاء الأيقونة
    icon = create_app_icon()
    
    # حفظ الأيقونة
    output_path = "assets/icons/app_icon.png"
    
    # التأكد من وجود المجلد
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # حفظ الأيقونة
    icon.save(output_path, "PNG")
    print(f"✅ تم حفظ الأيقونة في: {output_path}")
    
    # إنشاء أحجام مختلفة
    sizes = [36, 48, 72, 96, 144, 192, 512]
    for size in sizes:
        resized_icon = icon.resize((size, size), Image.Resampling.LANCZOS)
        size_path = f"assets/icons/app_icon_{size}.png"
        resized_icon.save(size_path, "PNG")
        print(f"✅ تم حفظ الأيقونة بحجم {size}x{size}")
    
    print("\n🚀 تم إنشاء جميع الأيقونات بنجاح!")
    print("الآن قم بتشغيل: flutter pub run flutter_launcher_icons:main")

if __name__ == "__main__":
    main()
