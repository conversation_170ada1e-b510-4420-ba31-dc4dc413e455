import 'package:flutter/material.dart';
import 'lib/database/database_helper.dart';
import 'lib/models/customer.dart';
import 'lib/models/debt.dart';

void main() {
  runApp(const DatabaseTestApp());
}

class DatabaseTestApp extends StatelessWidget {
  const DatabaseTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'اختبار قاعدة البيانات',
      home: DatabaseTestScreen(),
    );
  }
}

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({super.key});

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  final DatabaseHelper _db = DatabaseHelper();
  String _status = 'جاري الاختبار...';
  final List<String> _results = [];
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _testDatabase();
  }

  Future<void> _testDatabase() async {
    try {
      _addResult('🔄 بدء اختبار قاعدة البيانات...');

      // اختبار 1: تهيئة قاعدة البيانات
      _addResult('📊 اختبار تهيئة قاعدة البيانات...');
      await _db.database;
      _addResult('✅ تم تهيئة قاعدة البيانات بنجاح');

      // اختبار 2: تحميل العملاء
      _addResult('👥 اختبار تحميل العملاء...');
      final customers = await _db.getAllCustomers();
      _addResult('✅ تم تحميل ${customers.length} عميل');

      // اختبار 3: تحميل الديون
      _addResult('💰 اختبار تحميل الديون...');
      final debts = await _db.getAllDebts();
      _addResult('✅ تم تحميل ${debts.length} دين');

      // اختبار 4: إنشاء عميل تجريبي
      _addResult('➕ اختبار إنشاء عميل تجريبي...');
      final now = DateTime.now();
      final testCustomer = Customer(
        name: 'أحمد محمد',
        phone: '0501234567',
        creditLimit: 1000.0,
        limitNotes: 'عميل مميز - ملاحظات تجريبية',
        createdAt: now,
        updatedAt: now,
      );

      final customerId = await _db.insertCustomer(testCustomer);
      _addResult('✅ تم إنشاء عميل تجريبي بالمعرف: $customerId');

      // اختبار 5: إنشاء دين تجريبي
      _addResult('💳 اختبار إنشاء دين تجريبي...');
      final testDebt = Debt(
        customerId: customerId,
        itemName: 'عنصر تجريبي',
        quantity: 1,
        amount: 100.0,
        cardType: 'cash',
        notes: 'دين تجريبي',
        entryDate: now,
        dueDate: now.add(const Duration(days: 30)),
        createdAt: now,
        updatedAt: now,
      );

      final debtId = await _db.insertDebt(testDebt);
      _addResult('✅ تم إنشاء دين تجريبي بالمعرف: $debtId');

      // اختبار 6: حذف البيانات التجريبية
      _addResult('🗑️ تنظيف البيانات التجريبية...');
      await _db.deleteDebt(debtId);
      await _db.deleteCustomer(customerId);
      _addResult('✅ تم حذف البيانات التجريبية');

      setState(() {
        _status = 'تم اجتياز جميع الاختبارات بنجاح! ✅';
      });
    } catch (e, stackTrace) {
      _addResult('❌ خطأ في اختبار قاعدة البيانات: $e');
      _addResult('Stack trace: $stackTrace');

      setState(() {
        _hasError = true;
        _status = 'فشل في اختبار قاعدة البيانات ❌';
      });
    }
  }

  void _addResult(String result) {
    setState(() {
      _results.add(result);
    });
    debugPrint(result);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار قاعدة البيانات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              color: _hasError ? Colors.red[50] : Colors.green[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _hasError ? Icons.error : Icons.check_circle,
                      size: 48,
                      color: _hasError ? Colors.red : Colors.green,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سجل الاختبارات:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ListView.builder(
                  itemCount: _results.length,
                  itemBuilder: (context, index) {
                    final result = _results[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        result,
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                          color: result.startsWith('❌')
                              ? Colors.red
                              : result.startsWith('✅')
                                  ? Colors.green
                                  : Colors.black87,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _results.clear();
                  _hasError = false;
                  _status = 'جاري الاختبار...';
                });
                _testDatabase();
              },
              child: const Text('إعادة الاختبار'),
            ),
          ],
        ),
      ),
    );
  }
}
