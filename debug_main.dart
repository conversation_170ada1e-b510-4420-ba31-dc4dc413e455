import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// استيراد مبسط للاختبار
import 'lib/providers/customer_provider.dart';
import 'lib/providers/debt_provider.dart';
import 'lib/providers/font_provider.dart';

void main() async {
  print('🚀 بدء تشغيل التطبيق...');

  try {
    WidgetsFlutterBinding.ensureInitialized();
    print('✅ تم تهيئة WidgetsFlutterBinding');

    // تهيئة اللغة العربية
    await initializeDateFormatting('ar');
    print('✅ تم تهيئة اللغة العربية');

    print('✅ جميع التهيئات تمت بنجاح، تشغيل التطبيق...');
    runApp(const DebugApp());
  } catch (e, stackTrace) {
    print('❌ خطأ في التهيئة: $e');
    print('Stack trace: $stackTrace');

    // تشغيل تطبيق خطأ بسيط
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('خطأ في التهيئة'),
                const SizedBox(height: 8),
                Text('$e'),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DebugApp extends StatelessWidget {
  const DebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    print('🏗️ بناء DebugApp...');

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) {
            print('🔧 إنشاء CustomerProvider...');
            return CustomerProvider();
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            print('🔧 إنشاء DebtProvider...');
            return DebtProvider();
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            print('🔧 إنشاء FontProvider...');
            return FontProvider();
          },
        ),
      ],
      child: MaterialApp(
        title: 'اختبار التطبيق',
        debugShowCheckedModeBanner: false,
        locale: const Locale('ar', 'SA'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('ar', 'SA')],
        theme: ThemeData(primarySwatch: Colors.teal, fontFamily: 'Arial'),
        home: const DebugScreen(),
      ),
    );
  }
}

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  String status = 'جاري التحميل...';
  bool hasError = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    print('🔄 تهيئة DebugScreen...');
    _testProviders();
  }

  Future<void> _testProviders() async {
    try {
      print('🧪 اختبار Providers...');

      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      print('✅ CustomerProvider متاح');

      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      print('✅ DebtProvider متاح');

      Provider.of<FontProvider>(context, listen: false);
      print('✅ FontProvider متاح');

      // اختبار تحميل البيانات
      await customerProvider.loadCustomers();
      print('✅ تم تحميل العملاء: ${customerProvider.customers.length}');

      await debtProvider.loadAllDebts();
      print('✅ تم تحميل الديون: ${debtProvider.debts.length}');

      setState(() {
        status = 'تم تحميل جميع البيانات بنجاح!';
      });
    } catch (e, stackTrace) {
      print('❌ خطأ في اختبار Providers: $e');
      print('Stack trace: $stackTrace');

      setState(() {
        hasError = true;
        errorMessage = e.toString();
        status = 'حدث خطأ في التحميل';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🎨 بناء DebugScreen UI...');

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار التطبيق'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      hasError ? Icons.error : Icons.check_circle,
                      size: 64,
                      color: hasError ? Colors.red : Colors.green,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      status,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (hasError) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        ),
                        child: Text(
                          'تفاصيل الخطأ:\n$errorMessage',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Consumer<CustomerProvider>(
              builder: (context, customerProvider, _) {
                return Card(
                  child: ListTile(
                    leading: const Icon(Icons.people),
                    title: const Text('العملاء'),
                    subtitle: Text(
                      'العدد: ${customerProvider.customers.length}',
                    ),
                    trailing: customerProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Icon(Icons.check),
                  ),
                );
              },
            ),
            Consumer<DebtProvider>(
              builder: (context, debtProvider, _) {
                return Card(
                  child: ListTile(
                    leading: const Icon(Icons.account_balance_wallet),
                    title: const Text('الديون'),
                    subtitle: Text('العدد: ${debtProvider.debts.length}'),
                    trailing: debtProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Icon(Icons.check),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
