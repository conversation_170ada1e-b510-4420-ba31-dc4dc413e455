{"build_settings": {"app_name": "محاسب_ديون_احترافي", "auto_build_enabled": true, "build_delay_seconds": 10, "create_timestamped_copies": true, "builds_directory": "builds", "distribution_directory": "distribution"}, "flutter_build_options": {"release": {"shrink": true, "obfuscate": true, "split_debug_info": true, "debug_info_path": "build/debug-info", "tree_shake_icons": true, "target_platform": "android-arm,android-arm64,android-x64"}, "debug": {"shrink": false, "obfuscate": false, "split_debug_info": false, "verbose": true}, "profile": {"shrink": true, "obfuscate": false, "split_debug_info": true, "debug_info_path": "build/debug-info"}}, "file_watcher": {"enabled": true, "watch_patterns": ["lib/**/*.dart", "assets/**/*", "pubspec.yaml", "android/app/src/main/AndroidManifest.xml", "android/app/build.gradle.kts"], "ignore_patterns": ["**/*.g.dart", "**/*.freezed.dart", "**/generated_plugin_registrant.dart", "build/**", ".dart_tool/**", ".git/**", "**/.DS_Store", "**/Thumbs.db", "**/*.log", "**/node_modules/**"], "scan_interval_seconds": 2, "build_delay_seconds": 10}, "notifications": {"show_build_start": true, "show_build_complete": true, "show_build_error": true, "sound_enabled": false}, "optimization": {"clean_before_build": true, "parallel_build": true, "cache_enabled": true, "incremental_build": true}, "apk_settings": {"min_sdk_version": 23, "target_sdk_version": 34, "compile_sdk_version": 34, "version_name": "1.0.0", "version_code": 1, "application_id": "com.example.mahasb"}, "signing": {"debug_keystore": "android/app/debug.keystore", "release_keystore": null, "key_alias": null, "store_password": null, "key_password": null}, "performance": {"enable_r8": true, "enable_proguard": false, "multidex_enabled": true, "gradle_parallel": true, "gradle_daemon": true, "gradle_caching": true}, "development": {"hot_reload": true, "hot_restart": true, "preserve_state": true, "fast_refresh": true, "debug_mode": true, "verbose_logging": false}, "distribution": {"create_release_notes": true, "include_version_in_filename": true, "compress_apk": false, "generate_checksums": true, "backup_previous_version": true}, "git_integration": {"auto_commit_builds": false, "commit_message_template": "Build APK: {version} - {timestamp}", "tag_releases": false, "push_after_build": false}, "advanced": {"custom_build_script": null, "pre_build_commands": [], "post_build_commands": [], "environment_variables": {}, "build_timeout_minutes": 30}}