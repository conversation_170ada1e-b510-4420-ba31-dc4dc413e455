# 🏗️ دليل بناء APK التلقائي
## محاسب ديون احترافي - Professional Debt Accountant

---

## 📋 نظرة عامة

تم إعداد نظام بناء APK تلقائي متقدم يتيح لك:
- بناء APK تلقائياً عند حفظ التغييرات
- مراقبة الملفات في الوقت الفعلي
- إنشاء نسخ مؤرخة من APK
- تحسين الأداء والحجم

---

## 🚀 البدء السريع

### 1. البناء السريع (للتطوير)
```bash
# تشغيل مدير البناء
build_manager.bat

# أو البناء السريع مباشرة
scripts\quick_build_apk.bat
```

### 2. البناء الكامل (للإنتاج)
```bash
scripts\auto_build_apk.bat
```

### 3. المراقبة التلقائية
```bash
scripts\watch_and_build.bat
```

---

## 📁 هيكل الملفات

```
📦 mahasb/
├── 🏗️ build_manager.bat          # مدير البناء الرئيسي
├── 📱 محاسب_ديون_احترافي.apk    # APK الحالي
├── 📂 builds/                    # النسخ المؤرخة
├── 📂 scripts/
│   ├── 🔧 auto_build_apk.bat     # بناء كامل
│   ├── ⚡ quick_build_apk.bat    # بناء سريع
│   ├── 👁️ watch_and_build.bat   # مراقبة الملفات
│   └── 🐍 file_watcher_apk.py   # مراقب Python
└── 📂 lib/                      # كود التطبيق
```

---

## 🔧 خيارات البناء

### 1. البناء الكامل (Optimized)
- **الملف**: `scripts\auto_build_apk.bat`
- **المميزات**:
  - تنظيف البناء السابق
  - تحسين الحجم (`--shrink`)
  - إخفاء الكود (`--obfuscate`)
  - معلومات التصحيح منفصلة
  - نسخة مؤرخة تلقائياً

### 2. البناء السريع (Development)
- **الملف**: `scripts\quick_build_apk.bat`
- **المميزات**:
  - بناء سريع بدون تنظيف
  - مناسب للتطوير والاختبار
  - يحافظ على ملفات البناء السابقة

### 3. المراقبة التلقائية (Auto Watch)
- **الملف**: `scripts\watch_and_build.bat`
- **المميزات**:
  - مراقبة الملفات في الوقت الفعلي
  - بناء تلقائي عند الحفظ
  - تأخير ذكي لتجنب البناء المتكرر

---

## 👁️ الملفات المراقبة

### ✅ يتم مراقبة:
- `lib/**/*.dart` - ملفات Dart
- `assets/**/*` - الأصول والصور
- `pubspec.yaml` - إعدادات المشروع
- `android/app/src/main/AndroidManifest.xml`
- `android/app/build.gradle.kts`

### ❌ يتم تجاهل:
- `**/*.g.dart` - الملفات المولدة
- `**/*.freezed.dart` - ملفات Freezed
- `build/**` - ملفات البناء
- `.dart_tool/**` - أدوات Dart
- `.git/**` - ملفات Git

---

## ⚙️ الإعدادات المتقدمة

### تخصيص مراقب الملفات
يمكنك تعديل `scripts\file_watcher_apk.py`:

```python
# تغيير تأخير البناء (بالثواني)
self.build_delay = 10

# إضافة أنماط ملفات جديدة
self.watch_patterns.append('custom/**/*.dart')

# إضافة ملفات للتجاهل
self.ignore_patterns.append('**/temp/**')
```

### تحسين الأداء
في `scripts\auto_build_apk.bat`:

```batch
REM للحصول على أفضل ضغط
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info

REM للبناء السريع
flutter build apk --release
```

---

## 📊 معلومات APK

### حجم APK المتوقع:
- **مع التحسين**: 15-25 MB
- **بدون تحسين**: 25-35 MB

### الأذونات المطلوبة:
- `INTERNET` - للاتصال بـ Firebase
- `WRITE_EXTERNAL_STORAGE` - لحفظ النسخ الاحتياطية
- `READ_CONTACTS` - لاختيار جهات الاتصال

---

## 🐛 حل المشاكل

### مشكلة: Flutter غير موجود
```bash
# تأكد من تثبيت Flutter وإضافته لـ PATH
flutter doctor
```

### مشكلة: فشل البناء
```bash
# تنظيف وإعادة البناء
flutter clean
flutter pub get
flutter build apk --release
```

### مشكلة: Python غير موجود
```bash
# تثبيت Python من python.org
# أو استخدام البناء اليدوي بدلاً من المراقبة
```

### مشكلة: APK كبير الحجم
```bash
# استخدم البناء المحسن
scripts\auto_build_apk.bat

# أو قم بتحليل الحجم
flutter build apk --analyze-size
```

---

## 📱 اختبار APK

### على المحاكي:
```bash
# تثبيت APK على المحاكي
adb install محاسب_ديون_احترافي.apk
```

### على الجهاز الحقيقي:
1. تفعيل "مصادر غير معروفة" في الإعدادات
2. نقل APK للجهاز
3. تثبيت APK

---

## 🔄 سير العمل المقترح

### للتطوير اليومي:
1. تشغيل `scripts\watch_and_build.bat`
2. العمل على الكود عادياً
3. APK يتم تحديثه تلقائياً

### للإصدار النهائي:
1. تشغيل `scripts\auto_build_apk.bat`
2. اختبار APK على أجهزة مختلفة
3. رفع APK للتوزيع

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من `flutter doctor`
2. راجع رسائل الخطأ في النافذة
3. تأكد من وجود جميع المتطلبات

---

## 🎯 نصائح للأداء الأفضل

1. **استخدم البناء السريع أثناء التطوير**
2. **استخدم البناء الكامل للإصدارات**
3. **راقب حجم APK بانتظام**
4. **اختبر على أجهزة مختلفة**
5. **احتفظ بنسخ مؤرخة للإصدارات المهمة**

---

*تم إنشاء هذا الدليل لمساعدتك في الحصول على أفضل تجربة تطوير مع نظام بناء APK التلقائي* 🚀
