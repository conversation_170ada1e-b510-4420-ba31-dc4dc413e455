import 'package:flutter/material.dart';

// منتقي تاريخ مخصص مع أسماء الأشهر بالأرقام العربية
class ArabicMonthDatePicker {
  // أسماء الأشهر بالأرقام العربية
  static const List<String> arabicMonthNames = [
    'الأول', // يناير
    'الثاني', // فبراير
    'الثالث', // مارس
    'الرابع', // أبريل
    'الخامس', // مايو
    'السادس', // يونيو
    'السابع', // يوليو
    'الثامن', // أغسطس
    'التاسع', // سبتمبر
    'العاشر', // أكتوبر
    'الحادي عشر', // نوفمبر
    'الثاني عشر', // ديسمبر
  ];

  // عرض منتقي التاريخ مع أسماء الأشهر بالأرقام العربية
  static Future<DateTime?> showArabicDatePicker({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required String helpText,
    required bool isEntryDate,
  }) async {
    // استخدام منتقي التاريخ المخصص مع أسماء الأشهر بالأرقام العربية
    return showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CustomArabicDatePicker(
        initialDate: initialDate,
        firstDate: firstDate,
        lastDate: lastDate,
        helpText: helpText,
        isEntryDate: isEntryDate,
      ),
    );
  }
}

// دالة مساعدة لتحويل اسم الشهر إلى الرقم العربي
String getArabicMonthName(int month) {
  if (month >= 1 && month <= 12) {
    return ArabicMonthDatePicker.arabicMonthNames[month - 1];
  }
  return 'غير معروف';
}

// دالة لتنسيق التاريخ مع اسم الشهر بالرقم العربي
String formatDateWithArabicMonth(DateTime date) {
  final arabicMonth = getArabicMonthName(date.month);
  return '${date.day} $arabicMonth ${date.year}';
}

// منتقي تاريخ مخصص مع أسماء الأشهر بالأرقام العربية
class _CustomArabicDatePicker extends StatefulWidget {
  const _CustomArabicDatePicker({
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
    required this.helpText,
    required this.isEntryDate,
  });
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;
  final String helpText;
  final bool isEntryDate;

  @override
  State<_CustomArabicDatePicker> createState() =>
      _CustomArabicDatePickerState();
}

class _CustomArabicDatePickerState extends State<_CustomArabicDatePicker> {
  late DateTime selectedDate;
  late int selectedYear;
  late int selectedMonth;
  late int selectedDay;

  @override
  void initState() {
    super.initState();
    selectedDate = widget.initialDate;
    selectedYear = selectedDate.year;
    selectedMonth = selectedDate.month;
    selectedDay = selectedDate.day;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: widget.isEntryDate
                    ? [Colors.blue.shade600, Colors.blue.shade800]
                    : [Colors.orange.shade600, Colors.orange.shade800],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.helpText,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // Date Display
          Container(
            padding: const EdgeInsets.all(20),
            child: Text(
              formatDateWithArabicMonth(selectedDate),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: widget.isEntryDate
                    ? Colors.blue.shade800
                    : Colors.orange.shade800,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Date Pickers
          Expanded(
            child: Row(
              children: [
                // Year Picker
                Expanded(
                  child: _buildPickerColumn(
                    title: 'السنة',
                    items: List.generate(
                      widget.lastDate.year - widget.firstDate.year + 1,
                      (index) => widget.firstDate.year + index,
                    ).map((year) => year.toString()).toList(),
                    selectedIndex: selectedYear - widget.firstDate.year,
                    onChanged: (index) {
                      setState(() {
                        selectedYear = widget.firstDate.year + index;
                        _updateSelectedDate();
                      });
                    },
                  ),
                ),

                // Month Picker
                Expanded(
                  child: _buildPickerColumn(
                    title: 'الشهر',
                    items: ArabicMonthDatePicker.arabicMonthNames,
                    selectedIndex: selectedMonth - 1,
                    onChanged: (index) {
                      setState(() {
                        selectedMonth = index + 1;
                        _updateSelectedDate();
                      });
                    },
                  ),
                ),

                // Day Picker
                Expanded(
                  child: _buildPickerColumn(
                    title: 'اليوم',
                    items: List.generate(
                      _getDaysInMonth(selectedYear, selectedMonth),
                      (index) => (index + 1).toString(),
                    ),
                    selectedIndex: selectedDay - 1,
                    onChanged: (index) {
                      setState(() {
                        selectedDay = index + 1;
                        _updateSelectedDate();
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // Action Buttons
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade300,
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'إلغاء',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(selectedDate),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.isEntryDate
                          ? Colors.blue.shade600
                          : Colors.orange.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'تأكيد',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickerColumn({
    required String title,
    required List<String> items,
    required int selectedIndex,
    required Function(int) onChanged,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: widget.isEntryDate
                  ? Colors.blue.shade800
                  : Colors.orange.shade800,
            ),
          ),
        ),
        Expanded(
          child: ListWheelScrollView.useDelegate(
            itemExtent: 50,
            controller: FixedExtentScrollController(initialItem: selectedIndex),
            onSelectedItemChanged: onChanged,
            physics: const FixedExtentScrollPhysics(),
            childDelegate: ListWheelChildBuilderDelegate(
              builder: (context, index) {
                if (index < 0 || index >= items.length) return null;
                final isSelected = index == selectedIndex;
                return Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? (widget.isEntryDate
                            ? Colors.blue.shade100
                            : Colors.orange.shade100)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    items[index],
                    style: TextStyle(
                      fontSize: isSelected ? 18 : 16,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? (widget.isEntryDate
                              ? Colors.blue.shade800
                              : Colors.orange.shade800)
                          : Colors.black87,
                    ),
                  ),
                );
              },
              childCount: items.length,
            ),
          ),
        ),
      ],
    );
  }

  void _updateSelectedDate() {
    final maxDay = _getDaysInMonth(selectedYear, selectedMonth);
    if (selectedDay > maxDay) {
      selectedDay = maxDay;
    }
    selectedDate = DateTime(selectedYear, selectedMonth, selectedDay);
  }

  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
}
