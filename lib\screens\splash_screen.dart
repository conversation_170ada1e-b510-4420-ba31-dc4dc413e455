import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'home_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;
  late AnimationController _particleController;

  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<double> _progressAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    // تهيئة الرسوم المتحركة
    _logoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    _textAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _textController, curve: Curves.easeOutBack),
        );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.bounceOut),
    );

    _startAnimations();
  }

  void _startAnimations() async {
    try {
      debugPrint('🎬 بدء الرسوم المتحركة...');

      // بدء الرسوم المتحركة بالتتابع
      _particleController.repeat();

      await Future.delayed(const Duration(milliseconds: 300));
      if (mounted) _logoController.forward();

      await Future.delayed(const Duration(milliseconds: 600));
      if (mounted) _textController.forward();

      await Future.delayed(const Duration(milliseconds: 400));
      if (mounted) _progressController.forward();

      // انتظار انتهاء جميع الرسوم المتحركة ثم الانتقال
      await Future.delayed(const Duration(milliseconds: 3000));

      debugPrint('🎬 انتهت الرسوم المتحركة، الانتقال للشاشة الرئيسية...');

      if (mounted) {
        try {
          Navigator.of(context).pushReplacement(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const HomeScreen(),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    return SlideTransition(
                      position:
                          Tween<Offset>(
                            begin: const Offset(1.0, 0.0),
                            end: Offset.zero,
                          ).animate(
                            CurvedAnimation(
                              parent: animation,
                              curve: Curves.easeInOutCubic,
                            ),
                          ),
                      child: FadeTransition(opacity: animation, child: child),
                    );
                  },
              transitionDuration: const Duration(milliseconds: 1000),
            ),
          );
          debugPrint('✅ تم الانتقال للشاشة الرئيسية بنجاح');
        } catch (e) {
          debugPrint('❌ خطأ في الانتقال للشاشة الرئيسية: $e');
          // محاولة انتقال بسيط في حالة الخطأ
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في الرسوم المتحركة: $e');
      // الانتقال المباشر في حالة فشل الرسوم المتحركة
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    }
  }

  @override
  void dispose() {
    try {
      _logoController.dispose();
      _textController.dispose();
      _progressController.dispose();
      _particleController.dispose();
      debugPrint('✅ تم تنظيف موارد SplashScreen بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف موارد SplashScreen: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF0F2027), Color(0xFF203A43), Color(0xFF2C5364)],
          ),
        ),
        child: Stack(
          children: [
            // خلفية الجسيمات المتحركة
            AnimatedBuilder(
              animation: _particleController,
              builder: (context, child) {
                return CustomPaint(
                  painter: ParticlePainter(_particleController.value),
                  size: Size.infinite,
                );
              },
            ),

            SafeArea(
              child: Column(
                children: [
                  // الجزء العلوي - فارغ للتوسيط
                  const Expanded(flex: 2, child: SizedBox()),

                  // الجزء الأوسط - الأيقونة والنص
                  Expanded(
                    flex: 3,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب الأحجام بناءً على حجم الشاشة
                        final screenWidth = constraints.maxWidth;
                        final screenHeight = constraints.maxHeight;
                        final iconSize = (screenWidth * 0.35).clamp(
                          120.0,
                          180.0,
                        );

                        return Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // الأيقونة المتحركة الاحترافية
                            AnimatedBuilder(
                              animation: _logoAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _scaleAnimation.value,
                                  child: Transform.rotate(
                                    angle: _rotationAnimation.value * 0.1,
                                    child: Container(
                                      width: iconSize,
                                      height: iconSize,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: const LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Color(0xFF667eea),
                                            Color(0xFF764ba2),
                                          ],
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.3,
                                            ),
                                            blurRadius: 30,
                                            offset: const Offset(0, 15),
                                          ),
                                          BoxShadow(
                                            color: const Color(
                                              0xFF667eea,
                                            ).withValues(alpha: 0.3),
                                            blurRadius: 20,
                                            offset: const Offset(0, 5),
                                          ),
                                        ],
                                      ),
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          // أيقونة المحاسبة الرئيسية
                                          Container(
                                            width: 100,
                                            height: 100,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withValues(alpha: 0.1),
                                                  blurRadius: 10,
                                                  offset: const Offset(0, 5),
                                                ),
                                              ],
                                            ),
                                            child: const Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                // أيقونة الحاسبة
                                                Icon(
                                                  Icons.calculate_rounded,
                                                  size: 40,
                                                  color: Color(0xFF667eea),
                                                ),
                                                SizedBox(height: 4),
                                                // نص صغير
                                                Text(
                                                  'محاسب',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Color(0xFF667eea),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          // عناصر مالية متحركة
                                          Positioned(
                                            top: 20,
                                            right: 20,
                                            child: AnimatedBuilder(
                                              animation: _particleController,
                                              builder: (context, child) {
                                                return Transform.rotate(
                                                  angle:
                                                      _particleController
                                                          .value *
                                                      2 *
                                                      math.pi,
                                                  child: Container(
                                                    width: 24,
                                                    height: 24,
                                                    decoration: BoxDecoration(
                                                      gradient:
                                                          const LinearGradient(
                                                            colors: [
                                                              Color(0xFFFFD700),
                                                              Color(0xFFFFA500),
                                                            ],
                                                          ),
                                                      shape: BoxShape.circle,
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color:
                                                              const Color(
                                                                0xFFFFD700,
                                                              ).withValues(
                                                                alpha: 0.5,
                                                              ),
                                                          blurRadius: 8,
                                                          offset: const Offset(
                                                            0,
                                                            2,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    child: const Icon(
                                                      Icons.attach_money,
                                                      color: Colors.white,
                                                      size: 16,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),

                                          // بطاقة ائتمان
                                          Positioned(
                                            bottom: 15,
                                            left: 15,
                                            child: AnimatedBuilder(
                                              animation: _particleController,
                                              builder: (context, child) {
                                                return Transform.translate(
                                                  offset: Offset(
                                                    math.sin(
                                                          _particleController
                                                                  .value *
                                                              2 *
                                                              math.pi,
                                                        ) *
                                                        3,
                                                    math.cos(
                                                          _particleController
                                                                  .value *
                                                              2 *
                                                              math.pi,
                                                        ) *
                                                        3,
                                                  ),
                                                  child: Container(
                                                    width: 30,
                                                    height: 20,
                                                    decoration: BoxDecoration(
                                                      gradient:
                                                          const LinearGradient(
                                                            colors: [
                                                              Color(0xFF4CAF50),
                                                              Color(0xFF45a049),
                                                            ],
                                                          ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            4,
                                                          ),
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color:
                                                              const Color(
                                                                0xFF4CAF50,
                                                              ).withValues(
                                                                alpha: 0.5,
                                                              ),
                                                          blurRadius: 6,
                                                          offset: const Offset(
                                                            0,
                                                            2,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    child: const Icon(
                                                      Icons.credit_card,
                                                      color: Colors.white,
                                                      size: 12,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),

                            SizedBox(height: screenHeight * 0.05),

                            // النص المتحرك الاحترافي
                            SlideTransition(
                              position: _slideAnimation,
                              child: FadeTransition(
                                opacity: _textAnimation,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.1,
                                  ),
                                  child: Column(
                                    children: [
                                      ShaderMask(
                                        shaderCallback: (bounds) =>
                                            const LinearGradient(
                                              colors: [
                                                Color(0xFFFFFFFF),
                                                Color(0xFFE8E8E8),
                                              ],
                                            ).createShader(bounds),
                                        child: Text(
                                          'محاسب ديون احترافي',
                                          style: TextStyle(
                                            fontSize: (screenWidth * 0.08)
                                                .clamp(24.0, 36.0),
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            letterSpacing: 1.5,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      SizedBox(height: screenHeight * 0.015),
                                      Text(
                                        'إدارة الديون والمدفوعات بأناقة وسهولة',
                                        style: TextStyle(
                                          fontSize: (screenWidth * 0.04).clamp(
                                            14.0,
                                            18.0,
                                          ),
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                          letterSpacing: 0.8,
                                          height: 1.4,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: screenHeight * 0.01),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: screenWidth * 0.04,
                                          vertical: screenHeight * 0.008,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withValues(
                                            alpha: 0.1,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                          border: Border.all(
                                            color: Colors.white.withValues(
                                              alpha: 0.3,
                                            ),
                                          ),
                                        ),
                                        child: Text(
                                          'تطبيق متقدم للمحاسبة',
                                          style: TextStyle(
                                            fontSize: (screenWidth * 0.03)
                                                .clamp(10.0, 14.0),
                                            color: Colors.white.withValues(
                                              alpha: 0.8,
                                            ),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),

                  // الجزء السفلي - شريط التحميل المحسن
                  Expanded(
                    flex: 2,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final screenWidth = constraints.maxWidth;
                        final screenHeight = constraints.maxHeight;

                        return Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // شريط التحميل الاحترافي
                            AnimatedBuilder(
                              animation: _progressAnimation,
                              builder: (context, child) {
                                return Column(
                                  children: [
                                    Container(
                                      width: (screenWidth * 0.7).clamp(
                                        200.0,
                                        300.0,
                                      ),
                                      height: (screenHeight * 0.015).clamp(
                                        4.0,
                                        8.0,
                                      ),
                                      margin: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.1,
                                      ),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(3),
                                        color: Colors.white.withValues(
                                          alpha: 0.2,
                                        ),
                                      ),
                                      child: Stack(
                                        children: [
                                          FractionallySizedBox(
                                            alignment: Alignment.centerLeft,
                                            widthFactor:
                                                _progressAnimation.value,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(3),
                                                gradient: const LinearGradient(
                                                  colors: [
                                                    Color(0xFF667eea),
                                                    Color(0xFF764ba2),
                                                  ],
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: const Color(
                                                      0xFF667eea,
                                                    ).withValues(alpha: 0.5),
                                                    blurRadius: 8,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.03),
                                    Text(
                                      '${(_progressAnimation.value * 100).toInt()}%',
                                      style: TextStyle(
                                        fontSize: (screenWidth * 0.035).clamp(
                                          12.0,
                                          16.0,
                                        ),
                                        color: Colors.white.withValues(
                                          alpha: 0.8,
                                        ),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),

                            SizedBox(height: screenHeight * 0.06),

                            // نص التحميل مع أيقونة
                            FadeTransition(
                              opacity: _progressAnimation,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: (screenWidth * 0.04).clamp(
                                      14.0,
                                      18.0,
                                    ),
                                    height: (screenWidth * 0.04).clamp(
                                      14.0,
                                      18.0,
                                    ),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white.withValues(alpha: 0.8),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: screenWidth * 0.03),
                                  Text(
                                    'جاري تحضير التطبيق...',
                                    style: TextStyle(
                                      fontSize: (screenWidth * 0.035).clamp(
                                        12.0,
                                        16.0,
                                      ),
                                      color: Colors.white.withValues(
                                        alpha: 0.8,
                                      ),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(height: screenHeight * 0.08),

                            // معلومات الإصدار والشركة
                            Column(
                              children: [
                                Text(
                                  'الإصدار 2.0.0',
                                  style: TextStyle(
                                    fontSize: (screenWidth * 0.03).clamp(
                                      10.0,
                                      14.0,
                                    ),
                                    color: Colors.white.withValues(alpha: 0.6),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                SizedBox(height: screenHeight * 0.008),
                                Text(
                                  'تطوير فريق التطبيقات المتقدمة',
                                  style: TextStyle(
                                    fontSize: (screenWidth * 0.025).clamp(
                                      8.0,
                                      12.0,
                                    ),
                                    color: Colors.white.withValues(alpha: 0.5),
                                  ),
                                ),
                              ],
                            ),

                            SizedBox(height: screenHeight * 0.06),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// رسام الجسيمات المتحركة
class ParticlePainter extends CustomPainter {
  ParticlePainter(this.animationValue);
  final double animationValue;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // رسم جسيمات متحركة
    for (int i = 0; i < 20; i++) {
      final x =
          (size.width * (i / 20)) +
          (math.sin(animationValue * 2 * math.pi + i) * 30);
      final y =
          (size.height * ((i % 5) / 5)) +
          (math.cos(animationValue * 2 * math.pi + i) * 20);

      final radius = 2 + math.sin(animationValue * 4 * math.pi + i) * 1;

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
