import 'package:flutter/material.dart';

class CustomerStatistics {
  CustomerStatistics({
    required this.customerId,
    required this.customerName,
    required this.totalDebtsAmount,
    required this.totalDebtsCount,
    required this.totalCardsCount,
    required this.todaySalesAmount,
    required this.todaySalesCount,
    required this.yesterdaySalesAmount,
    required this.yesterdaySalesCount,
    required this.weekSalesAmount,
    required this.weekSalesCount,
    required this.monthSalesAmount,
    required this.monthSalesCount,
    required this.overdueDebtsAmount,
    required this.overdueDebtsCount,
    required this.overdueCardTypes,
    required this.cardTypeQuantities,
    required this.dueTodayAmount,
    required this.dueTodayCount,
    required this.dueNearAmount,
    required this.dueNearCount,
    required this.paidTodayAmount,
    required this.paidTodayCount,
    required this.paidYesterdayAmount,
    required this.paidYesterdayCount,
    required this.paidWeekAmount,
    required this.paidWeekCount,
    required this.totalPaidCardsCount,
    required this.totalPaidAmount,
    required this.activity,
    required this.paymentBehavior,
    this.lastPaymentDate,
    this.lastPaymentTime,
    this.lastPaymentDay,
    required this.paymentCounter,
  });
  final String customerId;
  final String customerName;

  // إجمالي الديون
  final double totalDebtsAmount;
  final int totalDebtsCount;

  // إجمالي الكارتات
  final int totalCardsCount;

  // مبيعات اليوم
  final double todaySalesAmount;
  final int todaySalesCount;

  // مبيعات الأمس
  final double yesterdaySalesAmount;
  final int yesterdaySalesCount;

  // مبيعات الأسبوع
  final double weekSalesAmount;
  final int weekSalesCount;

  // مبيعات الشهر
  final double monthSalesAmount;
  final int monthSalesCount;

  // الديون المتأخرة
  final double overdueDebtsAmount;
  final int overdueDebtsCount;

  // أنواع الكارتات المتأخرة
  final Map<String, CardTypeQuantity> overdueCardTypes;

  // الكميات حسب النوع
  final Map<String, CardTypeQuantity> cardTypeQuantities;

  // المستحقات اليوم
  final double dueTodayAmount;
  final int dueTodayCount;

  // المستحقات القريبة (خلال 3 أيام)
  final double dueNearAmount;
  final int dueNearCount;

  // الديون المسددة اليوم
  final double paidTodayAmount;
  final int paidTodayCount;

  // الديون المسددة أمس
  final double paidYesterdayAmount;
  final int paidYesterdayCount;

  // الديون المسددة هذا الأسبوع
  final double paidWeekAmount;
  final int paidWeekCount;

  // إجمالي عدد الكارتات المسددة
  final int totalPaidCardsCount;

  // إجمالي المبلغ المسدد
  final double totalPaidAmount;

  // نشاط العميل
  final CustomerActivity activity;

  // سلوك السداد
  final PaymentBehavior paymentBehavior;

  // آخر عملية تسديد
  final DateTime? lastPaymentDate;
  final String? lastPaymentTime;
  final String? lastPaymentDay;
  final int paymentCounter;
}

class CardTypeQuantity {
  CardTypeQuantity({
    required this.cardType,
    required this.quantity,
    required this.amount,
  });
  final String cardType;
  final int quantity;
  final double amount;
}

enum CustomerActivity {
  veryActive, // نشط جداً
  active, // نشط
  moderate, // متوسط
  low, // قليل
  inactive, // غير نشط
}

enum PaymentBehavior {
  onTime, // يسدد في الموعد
  earlyPayer, // يسدد مبكراً
  latePayer, // يتأخر في السداد
  irregular, // غير منتظم
}

extension CustomerActivityExtension on CustomerActivity {
  String get displayName {
    switch (this) {
      case CustomerActivity.veryActive:
        return 'نشط جداً';
      case CustomerActivity.active:
        return 'نشط';
      case CustomerActivity.moderate:
        return 'متوسط';
      case CustomerActivity.low:
        return 'قليل';
      case CustomerActivity.inactive:
        return 'غير نشط';
    }
  }

  Color get color {
    switch (this) {
      case CustomerActivity.veryActive:
        return const Color(0xFF4CAF50);
      case CustomerActivity.active:
        return const Color(0xFF8BC34A);
      case CustomerActivity.moderate:
        return const Color(0xFFFF9800);
      case CustomerActivity.low:
        return const Color(0xFFFF5722);
      case CustomerActivity.inactive:
        return const Color(0xFF9E9E9E);
    }
  }
}

extension PaymentBehaviorExtension on PaymentBehavior {
  String get displayName {
    switch (this) {
      case PaymentBehavior.onTime:
        return 'منتظم';
      case PaymentBehavior.earlyPayer:
        return 'مبكر';
      case PaymentBehavior.latePayer:
        return 'متأخر';
      case PaymentBehavior.irregular:
        return 'غير منتظم';
    }
  }

  Color get color {
    switch (this) {
      case PaymentBehavior.onTime:
        return const Color(0xFF4CAF50);
      case PaymentBehavior.earlyPayer:
        return const Color(0xFF2196F3);
      case PaymentBehavior.latePayer:
        return const Color(0xFFF44336);
      case PaymentBehavior.irregular:
        return const Color(0xFFFF9800);
    }
  }
}
