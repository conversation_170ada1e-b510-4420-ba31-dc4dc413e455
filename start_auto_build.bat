@echo off
title Auto APK Builder - Professional Debt Accountant
color 0A

echo ========================================
echo    Auto APK Builder System
echo    Professional Debt Accountant
echo ========================================
echo.

REM Check if Flutter is installed
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Flutter not found in PATH
    echo Please install Flutter first
    echo.
    pause
    exit /b 1
)

echo [OK] Flutter found
echo.

REM Show current APK status
echo Current APK Status:
echo ==================
if exist "debt_accountant_pro.apk" (
    for %%A in ("debt_accountant_pro.apk") do (
        set /a size_mb=%%~zA/1024/1024
        echo [OK] debt_accountant_pro.apk - Size: %%~zA bytes
        echo     Last modified: %%~tA
    )
) else (
    echo [INFO] No APK found - will be created on first build
)
echo.

echo Available Options:
echo ==================
echo [1] Quick Build APK (Fast - for development)
echo [2] Full Build APK (Optimized - for production)
echo [3] Start File Watcher (Auto build on changes)
echo [4] Build Manager (All options)
echo [5] Clean and Rebuild
echo [0] Exit
echo.

set /p choice="Select option (0-5): "

if "%choice%"=="1" goto quick_build
if "%choice%"=="2" goto full_build
if "%choice%"=="3" goto file_watcher
if "%choice%"=="4" goto build_manager
if "%choice%"=="5" goto clean_rebuild
if "%choice%"=="0" goto exit
goto invalid

:quick_build
echo.
echo Starting Quick Build...
echo ======================
call scripts\quick_build_apk.bat
goto end

:full_build
echo.
echo Starting Full Build...
echo =====================
call scripts\auto_build_apk.bat
goto end

:file_watcher
echo.
echo Starting File Watcher...
echo =======================
echo This will monitor your files and automatically build APK when changes are detected.
echo Press Ctrl+C to stop watching.
echo.
call scripts\watch_and_build.bat
goto end

:build_manager
echo.
echo Opening Build Manager...
echo =======================
call build_manager.bat
goto end

:clean_rebuild
echo.
echo Clean and Rebuild...
echo ===================
echo This will delete all build files and rebuild from scratch.
set /p confirm="Are you sure? (y/n): "
if /i "%confirm%"=="y" (
    echo Cleaning...
    if exist "build" rmdir /s /q "build"
    if exist ".dart_tool" rmdir /s /q ".dart_tool"
    flutter clean
    flutter pub get
    echo.
    echo Starting full rebuild...
    call scripts\auto_build_apk.bat
) else (
    echo Cancelled.
)
goto end

:invalid
echo.
echo [ERROR] Invalid option. Please select 0-5.
echo.
pause
goto start

:start
cls
goto :eof

:end
echo.
echo ===========================================
echo Build process completed!
echo.
if exist "debt_accountant_pro.apk" (
    echo [SUCCESS] APK is ready: debt_accountant_pro.apk
    for %%A in ("debt_accountant_pro.apk") do (
        set /a size_mb=%%~zA/1024/1024
        echo File size: %%~zA bytes
    )
    echo.
    echo You can now:
    echo - Install on Android device
    echo - Test the application
    echo - Share with others
) else (
    echo [WARNING] APK file not found
    echo Please check for build errors
)
echo.
echo Press any key to return to menu or close window to exit...
pause >nul
cls
goto :eof

:exit
echo.
echo Thank you for using Auto APK Builder!
echo.
pause
