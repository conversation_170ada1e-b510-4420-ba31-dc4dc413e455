@echo off
echo ========================================
echo    Quick APK Build
echo ========================================
echo.

REM Check Flutter installation
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Flutter not found in PATH
    pause
    exit /b 1
)

echo Flutter found
echo.

REM Get current time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"

echo Starting quick build: %HH%:%Min%:%Sec%
echo.

REM Build APK without cleaning (faster)
echo Building APK...
flutter build apk --release
if %ERRORLEVEL% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Copying APK...

REM Copy APK
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    copy "build\app\outputs\flutter-apk\app-release.apk" "debt_accountant_pro.apk" >nul
    echo APK updated successfully
) else (
    echo APK file not found
    pause
    exit /b 1
)

REM Get end time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "HH2=%dt:~8,2%" & set "Min2=%dt:~10,2%" & set "Sec2=%dt:~12,2%"

echo.
echo Quick build completed successfully!
echo Finished at: %HH2%:%Min2%:%Sec2%
echo File: debt_accountant_pro.apk
echo.

REM Show file size
if exist "debt_accountant_pro.apk" (
    for %%A in ("debt_accountant_pro.apk") do (
        set /a size_mb=%%~zA/1024/1024
        echo APK size: %%~zA bytes
    )
)

echo.
echo Quick build does not include:
echo    - Clean previous build
echo    - Size optimizations
echo    - Code obfuscation
echo.
echo For full build use: auto_build_apk.bat
echo.
pause
