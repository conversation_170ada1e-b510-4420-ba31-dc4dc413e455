class Customer {
  // Constructor
  Customer({
    this.id,
    required this.name,
    this.phone,
    this.creditLimit,
    this.limitNotes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      phone: map['phone'],
      creditLimit: map['credit_limit']?.toDouble(),
      limitNotes: map['limit_notes'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] ?? 0),
    );
  }

  // Fields
  final int? id;
  final String name;
  final String? phone;
  final double? creditLimit;
  final String? limitNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'credit_limit': creditLimit,
      'limit_notes': limitNotes,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  Customer copyWith({
    int? id,
    String? name,
    String? phone,
    double? creditLimit,
    String? limitNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool clearPhone = false,
    bool clearCreditLimit = false,
    bool clearLimitNotes = false,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: clearPhone ? null : (phone ?? this.phone),
      creditLimit: clearCreditLimit ? null : (creditLimit ?? this.creditLimit),
      limitNotes: clearLimitNotes ? null : (limitNotes ?? this.limitNotes),
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Customer(id: $id, name: $name, phone: $phone, creditLimit: $creditLimit, limitNotes: $limitNotes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Customer &&
        other.id == id &&
        other.name == name &&
        other.phone == phone &&
        other.creditLimit == creditLimit &&
        other.limitNotes == limitNotes &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        phone.hashCode ^
        creditLimit.hashCode ^
        limitNotes.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
