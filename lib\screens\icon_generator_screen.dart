import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/create_app_icon.dart';
import 'dart:io';

class IconGeneratorScreen extends StatefulWidget {
  const IconGeneratorScreen({super.key});

  @override
  State<IconGeneratorScreen> createState() => _IconGeneratorScreenState();
}

class _IconGeneratorScreenState extends State<IconGeneratorScreen> {
  bool _isGenerating = false;
  String _status = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء أيقونة التطبيق الجديدة'),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // معاينة الأيقونة
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: FutureBuilder<Uint8List>(
                future: CreateAppIcon.generateNewIcon(size: 200),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.memory(
                        snapshot.data!,
                        width: 200,
                        height: 200,
                        fit: BoxFit.cover,
                      ),
                    );
                  } else {
                    return Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    );
                  }
                },
              ),
            ),

            const SizedBox(height: 40),

            // معلومات الأيقونة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  const Text(
                    'أيقونة التطبيق الجديدة',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF667eea),
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'تصميم احترافي مع تدرجات لونية أنيقة',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildFeature('🎨', 'تدرج احترافي'),
                      _buildFeature('💰', 'رموز مالية'),
                      _buildFeature('📱', 'متعدد الأحجام'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // زر إنشاء الأيقونة
            SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateIcons,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF667eea),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  elevation: 5,
                ),
                child: _isGenerating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Text(
                            'جاري الإنشاء...',
                            style: TextStyle(fontSize: 18),
                          ),
                        ],
                      )
                    : const Text(
                        'إنشاء جميع أحجام الأيقونة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 20),

            // حالة العملية
            if (_status.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: _status.contains('نجح')
                      ? Colors.green[100]
                      : Colors.orange[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _status.contains('نجح')
                        ? Colors.green
                        : Colors.orange,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _status.contains('نجح') ? Icons.check_circle : Icons.info,
                      color: _status.contains('نجح')
                          ? Colors.green
                          : Colors.orange,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        _status,
                        style: TextStyle(
                          color: _status.contains('نجح')
                              ? Colors.green[800]
                              : Colors.orange[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // تعليمات
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      Text(
                        'تعليمات:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Text(
                    '1. اضغط على "إنشاء جميع أحجام الأيقونة"\n'
                    '2. انتظر حتى اكتمال العملية\n'
                    '3. ستجد الأيقونات في مجلد assets/icons/\n'
                    '4. قم بتشغيل flutter pub get\n'
                    '5. قم بتشغيل flutter pub run flutter_launcher_icons:main',
                    style: TextStyle(color: Colors.blue[700], height: 1.5),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeature(String emoji, String text) {
    return Column(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 30)),
        const SizedBox(height: 5),
        Text(
          text,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Future<void> _generateIcons() async {
    setState(() {
      _isGenerating = true;
      _status = 'جاري إنشاء الأيقونات...';
    });

    try {
      // إنشاء جميع أحجام الأيقونة
      final icons = await CreateAppIcon.generateAllSizes();

      // حفظ الأيقونات
      for (final entry in icons.entries) {
        final fileName = entry.key;
        final data = entry.value;

        // حفظ في مجلد assets/icons
        final file = File('assets/icons/$fileName');
        await file.writeAsBytes(data);
      }

      // إنشاء الأيقونة الرئيسية
      final mainIcon = await CreateAppIcon.generateNewIcon(size: 1024);
      final mainFile = File('assets/icons/app_icon.png');
      await mainFile.writeAsBytes(mainIcon);

      setState(() {
        _isGenerating = false;
        _status =
            'تم إنشاء جميع الأيقونات بنجاح! ✅\n'
            'الآن قم بتشغيل: flutter pub run flutter_launcher_icons:main';
      });
    } catch (e) {
      setState(() {
        _isGenerating = false;
        _status = 'حدث خطأ أثناء إنشاء الأيقونات: $e';
      });
    }
  }
}
