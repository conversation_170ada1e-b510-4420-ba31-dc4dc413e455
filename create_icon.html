<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة التطبيق</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #667eea;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .icon-preview {
            margin: 30px 0;
        }
        
        #iconCanvas {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونة التطبيق الاحترافية</h1>
        
        <div class="icon-preview">
            <canvas id="iconCanvas" width="300" height="300"></canvas>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3>تدرج احترافي</h3>
                <p>ألوان أنيقة ومتناسقة</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h3>رموز مالية</h3>
                <p>عملة وبطاقة ائتمان</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>جودة عالية</h3>
                <p>دقة 512x512 بكسل</p>
            </div>
        </div>
        
        <div class="buttons">
            <button onclick="generateIcon()">🎨 إنشاء الأيقونة</button>
            <button onclick="downloadIcon()">⬇️ تحميل PNG</button>
            <button onclick="downloadSVG()">📄 تحميل SVG</button>
        </div>
        
        <div class="info">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol style="text-align: right;">
                <li>اضغط على "إنشاء الأيقونة" لرؤية المعاينة</li>
                <li>اضغط على "تحميل PNG" لحفظ الأيقونة</li>
                <li>ضع الملف في مجلد assets/icons/app_icon.png</li>
                <li>قم بتشغيل: flutter pub run flutter_launcher_icons:main</li>
            </ol>
        </div>
    </div>

    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // تنظيف الكانفاس
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // خلفية متدرجة
            const bgGradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            bgGradient.addColorStop(0, '#0F2027');
            bgGradient.addColorStop(0.5, '#203A43');
            bgGradient.addColorStop(1, '#2C5364');
            
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, canvas.width/2, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة خارجية
            const outerGradient = ctx.createLinearGradient(60, 60, 240, 240);
            outerGradient.addColorStop(0, '#667eea');
            outerGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة بيضاء داخلية
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, 95, 0, 2 * Math.PI);
            ctx.fill();
            
            // أيقونة الحاسبة
            const iconGradient = ctx.createLinearGradient(120, 120, 180, 180);
            iconGradient.addColorStop(0, '#667eea');
            iconGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = iconGradient;
            ctx.roundRect(120, 120, 60, 60, 10);
            ctx.fill();
            
            // شاشة الحاسبة
            ctx.fillStyle = '#2C5364';
            ctx.roundRect(130, 130, 40, 15, 3);
            ctx.fill();
            
            // نص الشاشة
            ctx.fillStyle = '#00ff88';
            ctx.font = 'bold 8px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('123,456', 150, 141);
            
            // أزرار الحاسبة
            ctx.fillStyle = 'white';
            for(let row = 0; row < 3; row++) {
                for(let col = 0; col < 3; col++) {
                    const x = 132 + col * 12;
                    const y = 155 + row * 10;
                    ctx.roundRect(x, y, 8, 6, 1);
                    ctx.fill();
                }
            }
            
            // عملة ذهبية
            const goldGradient = ctx.createRadialGradient(220, 100, 0, 220, 100, 20);
            goldGradient.addColorStop(0, '#FFD700');
            goldGradient.addColorStop(1, '#FFA500');
            
            ctx.fillStyle = goldGradient;
            ctx.beginPath();
            ctx.arc(220, 100, 18, 0, 2 * Math.PI);
            ctx.fill();
            
            // رمز الدولار
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('$', 220, 106);
            
            // بطاقة ائتمان
            const cardGradient = ctx.createLinearGradient(70, 200, 110, 220);
            cardGradient.addColorStop(0, '#4CAF50');
            cardGradient.addColorStop(1, '#45a049');
            
            ctx.fillStyle = cardGradient;
            ctx.roundRect(70, 200, 30, 20, 3);
            ctx.fill();
            
            // خط مغناطيسي
            ctx.fillStyle = 'white';
            ctx.roundRect(75, 205, 20, 2, 1);
            ctx.fill();
            
            // نقاط البطاقة
            for(let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.arc(78 + i * 4, 215, 1, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // النص العربي
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('محاسب ديون', 150, 250);
            
            ctx.font = '12px Arial';
            ctx.fillText('احترافي', 150, 270);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            
            // إنشاء كانفاس بدقة 512x512
            const highResCanvas = document.createElement('canvas');
            highResCanvas.width = 512;
            highResCanvas.height = 512;
            const highResCtx = highResCanvas.getContext('2d');
            
            // رسم الأيقونة بدقة عالية
            drawHighResIcon(highResCtx, 512);
            
            // تحميل الصورة
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = highResCanvas.toDataURL();
            link.click();
        }
        
        function downloadSVG() {
            const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0F2027;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#203A43;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2C5364;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outerRing" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="goldGradient" cx="50%" cy="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45a049;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <circle cx="256" cy="256" r="256" fill="url(#bgGradient)"/>
  <circle cx="256" cy="256" r="200" fill="url(#outerRing)"/>
  <circle cx="256" cy="256" r="160" fill="white"/>
  
  <rect x="196" y="196" width="120" height="120" rx="25" fill="white"/>
  <rect x="216" y="216" width="80" height="80" rx="15" fill="url(#iconGradient)"/>
  <rect x="226" y="226" width="60" height="25" rx="5" fill="#2C5364"/>
  
  <text x="256" y="244" text-anchor="middle" fill="#00ff88" font-family="Arial" font-size="14" font-weight="bold">123,456</text>
  
  <g fill="white">
    <rect x="230" y="260" width="12" height="8" rx="2"/>
    <rect x="246" y="260" width="12" height="8" rx="2"/>
    <rect x="262" y="260" width="12" height="8" rx="2"/>
    <rect x="278" y="260" width="12" height="8" rx="2"/>
    <rect x="230" y="272" width="12" height="8" rx="2"/>
    <rect x="246" y="272" width="12" height="8" rx="2"/>
    <rect x="262" y="272" width="12" height="8" rx="2"/>
    <rect x="278" y="272" width="12" height="8" rx="2"/>
    <rect x="230" y="284" width="12" height="8" rx="2"/>
    <rect x="246" y="284" width="12" height="8" rx="2"/>
    <rect x="262" y="284" width="12" height="8" rx="2"/>
    <rect x="278" y="284" width="12" height="8" rx="2"/>
  </g>
  
  <circle cx="370" cy="170" r="30" fill="url(#goldGradient)" stroke="white" stroke-width="3"/>
  <text x="370" y="178" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">$</text>
  
  <rect x="120" y="340" width="50" height="32" rx="6" fill="url(#greenGradient)" stroke="white" stroke-width="3"/>
  <rect x="126" y="350" width="38" height="4" fill="white" opacity="0.8"/>
  <circle cx="132" cy="362" r="2" fill="white"/>
  <circle cx="138" cy="362" r="2" fill="white"/>
  <circle cx="144" cy="362" r="2" fill="white"/>
  <circle cx="150" cy="362" r="2" fill="white"/>
  
  <text x="256" y="400" text-anchor="middle" fill="white" font-family="Arial" font-size="28" font-weight="bold">محاسب ديون</text>
  <text x="256" y="430" text-anchor="middle" fill="white" font-family="Arial" font-size="18" opacity="0.9">احترافي</text>
</svg>`;
            
            const blob = new Blob([svgContent], {type: 'image/svg+xml'});
            const link = document.createElement('a');
            link.download = 'app_icon.svg';
            link.href = URL.createObjectURL(blob);
            link.click();
        }
        
        function drawHighResIcon(ctx, size) {
            const scale = size / 300;
            ctx.scale(scale, scale);
            
            // نفس كود الرسم ولكن بدقة عالية
            // خلفية متدرجة
            const bgGradient = ctx.createLinearGradient(0, 0, 300, 300);
            bgGradient.addColorStop(0, '#0F2027');
            bgGradient.addColorStop(0.5, '#203A43');
            bgGradient.addColorStop(1, '#2C5364');
            
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(150, 150, 150, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة خارجية
            const outerGradient = ctx.createLinearGradient(60, 60, 240, 240);
            outerGradient.addColorStop(0, '#667eea');
            outerGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(150, 150, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة بيضاء داخلية
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(150, 150, 95, 0, 2 * Math.PI);
            ctx.fill();
            
            // أيقونة الحاسبة
            const iconGradient = ctx.createLinearGradient(120, 120, 180, 180);
            iconGradient.addColorStop(0, '#667eea');
            iconGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = iconGradient;
            ctx.roundRect(120, 120, 60, 60, 10);
            ctx.fill();
            
            // شاشة الحاسبة
            ctx.fillStyle = '#2C5364';
            ctx.roundRect(130, 130, 40, 15, 3);
            ctx.fill();
            
            // نص الشاشة
            ctx.fillStyle = '#00ff88';
            ctx.font = 'bold 8px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('123,456', 150, 141);
            
            // أزرار الحاسبة
            ctx.fillStyle = 'white';
            for(let row = 0; row < 3; row++) {
                for(let col = 0; col < 3; col++) {
                    const x = 132 + col * 12;
                    const y = 155 + row * 10;
                    ctx.roundRect(x, y, 8, 6, 1);
                    ctx.fill();
                }
            }
            
            // عملة ذهبية
            const goldGradient = ctx.createRadialGradient(220, 100, 0, 220, 100, 20);
            goldGradient.addColorStop(0, '#FFD700');
            goldGradient.addColorStop(1, '#FFA500');
            
            ctx.fillStyle = goldGradient;
            ctx.beginPath();
            ctx.arc(220, 100, 18, 0, 2 * Math.PI);
            ctx.fill();
            
            // رمز الدولار
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('$', 220, 106);
            
            // بطاقة ائتمان
            const cardGradient = ctx.createLinearGradient(70, 200, 110, 220);
            cardGradient.addColorStop(0, '#4CAF50');
            cardGradient.addColorStop(1, '#45a049');
            
            ctx.fillStyle = cardGradient;
            ctx.roundRect(70, 200, 30, 20, 3);
            ctx.fill();
            
            // خط مغناطيسي
            ctx.fillStyle = 'white';
            ctx.roundRect(75, 205, 20, 2, 1);
            ctx.fill();
            
            // نقاط البطاقة
            for(let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.arc(78 + i * 4, 215, 1, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // النص العربي
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('محاسب ديون', 150, 250);
            
            ctx.font = '12px Arial';
            ctx.fillText('احترافي', 150, 270);
        }
        
        // إضافة دعم roundRect للمتصفحات القديمة
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // إنشاء الأيقونة عند تحميل الصفحة
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
