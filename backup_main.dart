import 'package:flutter/material.dart';

void main() {
  print('🚀 بدء تشغيل التطبيق البديل...');
  
  try {
    runApp(const BackupApp());
    print('✅ تم تشغيل التطبيق البديل بنجاح');
  } catch (e) {
    print('❌ خطأ في تشغيل التطبيق البديل: $e');
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('خطأ: $e'),
          ),
        ),
      ),
    );
  }
}

class BackupApp extends StatelessWidget {
  const BackupApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'محاسب ديون - نسخة بديلة',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Arial',
      ),
      home: const BackupHomeScreen(),
    );
  }
}

class BackupHomeScreen extends StatelessWidget {
  const BackupHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('محاسب ديون احترافي'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                size: 100,
                color: Colors.green,
              ),
              SizedBox(height: 20),
              Text(
                'التطبيق يعمل بنجاح!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10),
              Text(
                'هذه نسخة بديلة مبسطة من التطبيق',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 30),
              Card(
                elevation: 4,
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 48,
                        color: Colors.blue,
                      ),
                      SizedBox(height: 10),
                      Text(
                        'معلومات التشخيص',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        '• Flutter يعمل بشكل صحيح\n'
                        '• التطبيق يمكنه البناء والتشغيل\n'
                        '• المشكلة قد تكون في الكود المعقد\n'
                        '• يمكن البناء على هذه النسخة',
                        style: TextStyle(fontSize: 14),
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('التطبيق البديل يعمل بنجاح!'),
              backgroundColor: Colors.green,
            ),
          );
        },
        backgroundColor: Colors.teal,
        child: const Icon(Icons.play_arrow, color: Colors.white),
      ),
    );
  }
}
