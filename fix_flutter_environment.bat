@echo off
echo ========================================
echo    إصلاح بيئة Flutter الشامل
echo ========================================
echo.

echo 🛑 إيقاف جميع العمليات...
taskkill /f /im flutter.exe >nul 2>&1
taskkill /f /im dart.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1
taskkill /f /im java.exe >nul 2>&1

echo.
echo 🔍 فحص بيئة Flutter...
flutter --version
echo.
flutter doctor -v
echo.

echo 🧹 تنظيف شامل...
flutter clean
flutter pub cache clean
flutter pub cache repair

echo.
echo 📦 إعادة تحديث التبعيات...
flutter pub get

echo.
echo 🔧 إصلاح إضافي...
flutter precache
flutter config --clear-features

echo.
echo 🚀 اختبار التشغيل...
echo جاري اختبار التشغيل على Chrome...
timeout /t 3 >nul
flutter run -d chrome --debug --verbose

echo.
echo ========================================
echo    إذا لم يعمل، جرب الحلول التالية:
echo ========================================
echo.
echo 1. إعادة تثبيت Flutter:
echo    - احذف مجلد Flutter
echo    - حمل نسخة جديدة من flutter.dev
echo    - أعد تعيين متغيرات البيئة
echo.
echo 2. فحص متغيرات البيئة:
echo    - تأكد من PATH يحتوي على Flutter
echo    - تأكد من FLUTTER_ROOT
echo.
echo 3. تشغيل كـ Administrator:
echo    - انقر بزر الماوس الأيمن على CMD
echo    - اختر "Run as administrator"
echo.
pause
