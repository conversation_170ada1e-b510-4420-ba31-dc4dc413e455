# 🚀 نظام بناء APK التلقائي
## محاسب ديون احترافي - Professional Debt Accountant

---

## ✨ تم إعداد النظام بنجاح!

لديك الآن نظام بناء APK تلقائي متقدم يحفظ التغييرات في ملف APK بعد كل تعديل.

---

## 🎯 البدء السريع

### 1. تشغيل النظام
```bash
# انقر مرتين على الملف
start_auto_build.bat
```

### 2. الخيارات المتاحة
- **البناء السريع**: للتطوير والاختبار
- **البناء الكامل**: للإنتاج والتوزيع  
- **المراقبة التلقائية**: بناء تلقائي عند الحفظ
- **مدير البناء**: جميع الخيارات المتقدمة

---

## 📁 الملفات المهمة

```
📦 المشروع/
├── 🚀 start_auto_build.bat      # تشغيل النظام
├── 📱 debt_accountant_pro.apk   # APK الحالي
├── 🏗️ build_manager.bat        # مدير البناء المتقدم
├── 📂 builds/                  # النسخ المؤرخة
├── 📂 scripts/                 # سكريبتات البناء
└── 📖 BUILD_GUIDE.md           # الدليل المفصل
```

---

## ⚡ الاستخدام اليومي

### للتطوير:
1. شغل `start_auto_build.bat`
2. اختر "Start File Watcher" (خيار 3)
3. اعمل على الكود عادياً
4. APK يتم تحديثه تلقائياً عند الحفظ

### للإنتاج:
1. شغل `start_auto_build.bat`  
2. اختر "Full Build APK" (خيار 2)
3. انتظر انتهاء البناء
4. APK جاهز للتوزيع

---

## 📊 معلومات APK الحالي

- **الاسم**: `debt_accountant_pro.apk`
- **الحجم**: ~57 MB (مع التحسينات)
- **النوع**: Release APK
- **التحسينات**: ✅ مفعلة (ضغط + إخفاء كود)

---

## 🔧 الميزات المتقدمة

### 1. النسخ المؤرخة
- يتم حفظ نسخة مؤرخة في مجلد `builds/`
- تنسيق الاسم: `debt_accountant_YYYY-MM-DD_HH-MM-SS.apk`

### 2. المراقبة الذكية
- مراقبة ملفات `lib/` و `assets/`
- تجاهل الملفات المولدة تلقائياً
- تأخير ذكي لتجنب البناء المتكرر

### 3. التحسينات
- ضغط الحجم (`--shrink`)
- إخفاء الكود (`--obfuscate`)  
- تحسين الأيقونات
- إزالة الكود غير المستخدم

---

## 🐛 حل المشاكل

### مشكلة: "Flutter not found"
```bash
# تأكد من تثبيت Flutter
flutter doctor
```

### مشكلة: "Build failed"
```bash
# تنظيف وإعادة البناء
flutter clean
flutter pub get
```

### مشكلة: APK كبير الحجم
- استخدم "Full Build" بدلاً من "Quick Build"
- التحسينات تقلل الحجم بنسبة 30-40%

---

## 📱 اختبار APK

### على المحاكي:
```bash
adb install debt_accountant_pro.apk
```

### على الجهاز:
1. انسخ APK للجهاز
2. فعل "مصادر غير معروفة"
3. ثبت APK

---

## 🎯 نصائح مهمة

1. **استخدم المراقبة التلقائية أثناء التطوير**
2. **استخدم البناء الكامل للإصدارات النهائية**
3. **احتفظ بنسخ مؤرخة للإصدارات المهمة**
4. **اختبر APK على أجهزة مختلفة قبل التوزيع**

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع `BUILD_GUIDE.md` للتفاصيل الكاملة
2. تحقق من رسائل الخطأ في النافذة
3. تأكد من تحديث Flutter: `flutter upgrade`

---

## ✅ تم الإعداد بنجاح!

النظام جاهز للاستخدام. ابدأ بتشغيل `start_auto_build.bat` واستمتع ببناء APK تلقائي بعد كل تغيير! 🎉

---

*تم إنشاء هذا النظام خصيصاً لمشروع محاسب ديون احترافي* 💼
