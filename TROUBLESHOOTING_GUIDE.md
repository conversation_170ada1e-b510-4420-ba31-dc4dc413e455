# دليل حل مشكلة الشاشة البيضاء

## المشاكل المحتملة والحلول

### 1. مشاكل قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
flutter clean
flutter pub get
```

### 2. مشاكل التبعيات
```bash
# تحديث التبعيات
flutter pub upgrade --major-versions
flutter pub get
```

### 3. مشاكل الذاكرة
```bash
# تنظيف الذاكرة
flutter clean
rm -rf build/
flutter pub get
```

### 4. اختبار التطبيق خطوة بخطوة

#### الخطوة 1: اختبار Flutter
```bash
flutter doctor
flutter devices
```

#### الخطوة 2: اختبار التطبيق البسيط
```bash
flutter run test_app.dart
```

#### الخطوة 3: اختبار التطبيق مع التشخيص
```bash
flutter run debug_main.dart
```

#### الخطوة 4: تشغيل التطبيق الأصلي
```bash
flutter run lib/main.dart
```

### 5. فحص السجلات

#### في Android Studio:
1. افتح Flutter Inspector
2. تحقق من Console للأخطاء
3. استخدم Logcat لرؤية سجلات Android

#### في VS Code:
1. افتح Debug Console
2. تحقق من Output panel
3. ابحث عن رسائل الخطأ

### 6. حلول إضافية

#### إعادة تعيين التطبيق:
```bash
flutter clean
flutter pub cache repair
flutter pub get
flutter run
```

#### تشغيل في وضع Debug:
```bash
flutter run --debug --verbose
```

#### تشغيل في وضع Release:
```bash
flutter run --release
```

### 7. فحص الملفات المهمة

#### تحقق من:
- `lib/main.dart` - نقطة البداية
- `lib/screens/splash_screen.dart` - الشاشة الأولى
- `lib/screens/home_screen.dart` - الشاشة الرئيسية
- `lib/providers/` - جميع ملفات Providers
- `lib/database/database_helper.dart` - قاعدة البيانات

### 8. رسائل الخطأ الشائعة

#### "RenderFlex overflowed"
- مشكلة في تخطيط الواجهة
- الحل: تحقق من أحجام الويدجت

#### "Provider not found"
- مشكلة في تهيئة Providers
- الحل: تأكد من إضافة جميع Providers في main.dart

#### "Database locked"
- مشكلة في قاعدة البيانات
- الحل: أعد تشغيل التطبيق أو احذف قاعدة البيانات

#### "setState called after dispose"
- مشكلة في دورة حياة الويدجت
- الحل: أضف فحص mounted قبل setState

### 9. نصائح الأداء

#### تحسين الذاكرة:
- استخدم ListView.builder بدلاً من ListView
- تجنب إنشاء ويدجت جديدة في build()
- استخدم const constructors

#### تحسين قاعدة البيانات:
- أضف indexes للجداول
- استخدم transactions للعمليات المتعددة
- تجنب الاستعلامات المعقدة في UI thread

### 10. أدوات التشخيص

#### Flutter Inspector:
- فحص شجرة الويدجت
- تحليل الأداء
- فحص الذاكرة

#### Performance Overlay:
```dart
MaterialApp(
  showPerformanceOverlay: true,
  // ...
)
```

#### Debug Paint:
```dart
import 'package:flutter/rendering.dart';

void main() {
  debugPaintSizeEnabled = true;
  runApp(MyApp());
}
```

## خطوات الطوارئ

### إذا لم تعمل الحلول السابقة:

1. **إنشاء مشروع جديد:**
```bash
flutter create new_mahasb
# انسخ ملفات lib/ و assets/ للمشروع الجديد
```

2. **استعادة من نسخة احتياطية:**
- استخدم Git للعودة لآخر إصدار يعمل
- استعد قاعدة البيانات من نسخة احتياطية

3. **تشغيل في وضع آمن:**
- علق جميع Providers ما عدا الأساسية
- علق تحميل البيانات
- اختبر الواجهة فقط

## معلومات الاتصال للدعم

إذا استمرت المشكلة، يرجى تقديم:
- رسائل الخطأ الكاملة
- سجلات Flutter (flutter logs)
- معلومات الجهاز والنظام
- خطوات إعادة إنتاج المشكلة
