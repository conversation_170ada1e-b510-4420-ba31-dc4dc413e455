import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';

class CustomerInfoScreen extends StatefulWidget {
  const CustomerInfoScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerInfoScreen> createState() => _CustomerInfoScreenState();
}

class _CustomerInfoScreenState extends State<CustomerInfoScreen> {
  late Customer currentCustomer;

  @override
  void initState() {
    super.initState();
    currentCustomer = widget.customer;
  }

  String _formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString().replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );
    } else {
      return number
          .toStringAsFixed(2)
          .replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معلومات ${currentCustomer.name}'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      backgroundColor: Colors.grey.shade50,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Customer Info Card
            _buildCustomerInfoCard(),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة معلومات العميل
  Widget _buildCustomerInfoCard() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == currentCustomer.id)
            .toList();

        final totalAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final totalCards = customerDebts.length;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Customer Avatar and Name - مصغر
              Row(
                children: [
                  Container(
                    width: 45,
                    height: 45,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(22.5),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Icon(
                      Icons.person,
                      size: 24,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentCustomer.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2C3E50),
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'معلومات العميل',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Statistics - Amount Card (Full Width)
              _buildModernQuickStatCard(
                'إجمالي المبلغ',
                _formatNumber(totalAmount),
                Icons.account_balance_wallet,
                LinearGradient(
                  colors: [Colors.blue.shade50, Colors.blue.shade100],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                isFullWidth: true,
              ),

              const SizedBox(height: 16),

              // Statistics Row - Operations and Cards Count
              Row(
                children: [
                  Expanded(
                    child: _buildModernQuickStatCard(
                      'عدد العمليات',
                      '$totalCards',
                      Icons.receipt_long,
                      LinearGradient(
                        colors: [Colors.orange.shade50, Colors.orange.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildModernQuickStatCard(
                      'إجمالي الكروت',
                      '$totalCards',
                      Icons.credit_card,
                      LinearGradient(
                        colors: [Colors.green.shade50, Colors.green.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Customer Details
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل العميل',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildPhoneRow(),
                    const SizedBox(height: 12),
                    _buildCreationDateRow(),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.update,
                      'آخر تحديث',
                      '${currentCustomer.updatedAt.year}/${currentCustomer.updatedAt.month}/${currentCustomer.updatedAt.day}',
                      Colors.purple,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // بناء صف رقم الهاتف مع إمكانية التعديل
  Widget _buildPhoneRow() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.phone, size: 20, color: Colors.green),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'رقم الهاتف',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              GestureDetector(
                onTap: (currentCustomer.phone?.isNotEmpty == true)
                    ? () => _makePhoneCall(currentCustomer.phone!)
                    : null,
                child: Text(
                  (currentCustomer.phone?.isNotEmpty == true)
                      ? currentCustomer.phone!
                      : 'لم يتم إضافة رقم هاتف',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: (currentCustomer.phone?.isNotEmpty == true)
                        ? Colors.blue
                        : Colors.grey.shade500,
                  ),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => _showEditPhoneDialog(),
          icon: const Icon(Icons.edit, color: Colors.green, size: 20),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
        if (currentCustomer.phone?.isNotEmpty == true)
          IconButton(
            onPressed: () => _makePhoneCall(currentCustomer.phone!),
            icon: const Icon(Icons.call, color: Colors.green, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
      ],
    );
  }

  // بناء صف تاريخ الإضافة مع العداد
  Widget _buildCreationDateRow() {
    final now = DateTime.now();
    final createdAt = currentCustomer.createdAt;
    final difference = now.difference(createdAt);

    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();
    final days = difference.inDays % 30;

    String timeAgo = '';
    if (years > 0) {
      timeAgo = '$years سنة';
      if (months > 0) timeAgo += ' و $months شهر';
    } else if (months > 0) {
      timeAgo = '$months شهر';
      if (days > 0) timeAgo += ' و $days يوم';
    } else {
      timeAgo = '$days يوم';
    }

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.calendar_today,
            size: 20,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تاريخ الإضافة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${createdAt.year}/${createdAt.month}/${createdAt.day}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              Text(
                'منذ $timeAgo',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.orange.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // نافذة تعديل رقم الهاتف
  void _showEditPhoneDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _PhoneEditDialog(
        initialPhone: currentCustomer.phone ?? '',
        onSave: (newPhone) async {
          try {
            final customerProvider = Provider.of<CustomerProvider>(
              context,
              listen: false,
            );
            final cleanPhone = newPhone.trim();
            debugPrint('Original phone: "${currentCustomer.phone}"');
            debugPrint(
              'New phone: "$cleanPhone" (isEmpty: ${cleanPhone.isEmpty})',
            );

            final updatedCustomer = currentCustomer.copyWith(
              phone: cleanPhone.isEmpty ? null : cleanPhone,
              clearPhone: cleanPhone.isEmpty,
              updatedAt: DateTime.now(),
            );

            debugPrint('Updated customer phone: "${updatedCustomer.phone}"');
            await customerProvider.updateCustomer(updatedCustomer);

            if (context.mounted) {
              currentCustomer = updatedCustomer;

              Future.delayed(Duration.zero, () {
                if (mounted) {
                  setState(() {});
                }
              });

              final message = cleanPhone.isEmpty
                  ? 'Phone number deleted successfully'
                  : 'Phone number updated successfully';

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error updating phone number: $e'),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            }
          }
        },
      ),
    );
  }

  // بناء بطاقة إحصائية عصرية
  Widget _buildModernQuickStatCard(
    String title,
    String value,
    IconData icon,
    LinearGradient gradient, {
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isFullWidth
          ? Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Icon(icon, color: Colors.grey.shade600, size: 28),
                ),
                const SizedBox(width: 16),

                // Content Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        value,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),

                // Currency indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    'د.ع',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            )
          : Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Icon(icon, color: Colors.grey.shade600, size: 24),
                ),
                const SizedBox(height: 12),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.black54,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value,
    Color color, {
    bool isPhone = false,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isPhone)
                GestureDetector(
                  onTap: () => _makePhoneCall(value),
                  child: Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                )
              else
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
            ],
          ),
        ),
        if (isPhone)
          IconButton(
            onPressed: () => _makePhoneCall(value),
            icon: Icon(Icons.call, color: color, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
      ],
    );
  }

  // دالة الاتصال
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      await launchUrl(launchUri);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('لا يمكن الاتصال بالرقم: $phoneNumber'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// نافذة تعديل رقم الهاتف منفصلة
class _PhoneEditDialog extends StatefulWidget {
  const _PhoneEditDialog({required this.initialPhone, required this.onSave});
  final String initialPhone;
  final Function(String) onSave;

  @override
  State<_PhoneEditDialog> createState() => _PhoneEditDialogState();
}

class _PhoneEditDialogState extends State<_PhoneEditDialog> {
  late TextEditingController phoneController;

  @override
  void initState() {
    super.initState();
    phoneController = TextEditingController(text: widget.initialPhone);
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 16,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 400),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.phone, color: Colors.green, size: 24),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'تعديل رقم الهاتف',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // حقل رقم الهاتف
            TextField(
              controller: phoneController,
              keyboardType: TextInputType.phone,
              autofocus: true,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87, // إضافة لون النص
              ),
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                labelStyle: TextStyle(color: Colors.grey.shade600),
                hintText: 'أدخل رقم الهاتف أو اتركه فارغاً لحذفه',
                hintStyle: TextStyle(color: Colors.grey.shade400),
                prefixIcon: const Icon(Icons.phone, color: Colors.green),
                suffixIcon: phoneController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          phoneController.clear();
                          setState(() {});
                        },
                        icon: const Icon(Icons.clear, color: Colors.red),
                      )
                    : null,
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.green, width: 2),
                ),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),

            const SizedBox(height: 24),

            // الأزرار
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: Colors.grey.shade400),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'إلغاء',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      // التأكد من إرسال النص الصحيح (فارغ أو مملوء)
                      final phoneText = phoneController.text.trim();
                      debugPrint(
                        'Phone text to save: "$phoneText" (isEmpty: ${phoneText.isEmpty})',
                      );
                      await widget.onSave(phoneText);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'حفظ',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
