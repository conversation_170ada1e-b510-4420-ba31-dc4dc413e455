<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        canvas {
            border: 2px solid white;
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        button {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <h1>🎨 إنشاء أيقونة التطبيق الاحترافية</h1>
    <canvas id="iconCanvas" width="512" height="512"></canvas>
    <br>
    <button onclick="generateIcon()">إنشاء الأيقونة</button>
    <button onclick="downloadIcon()">تحميل PNG</button>
    
    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            const size = 512;
            
            // تنظيف الكانفاس
            ctx.clearRect(0, 0, size, size);
            
            // خلفية دائرية متدرجة
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#0F2027');
            bgGradient.addColorStop(0.5, '#203A43');
            bgGradient.addColorStop(1, '#2C5364');
            
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة خارجية مع تدرج
            const outerGradient = ctx.createLinearGradient(100, 100, 400, 400);
            outerGradient.addColorStop(0, '#667eea');
            outerGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, 200, 0, 2 * Math.PI);
            ctx.fill();
            
            // دائرة بيضاء داخلية
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(size/2, size/2, 160, 0, 2 * Math.PI);
            ctx.fill();
            
            // مربع أبيض للأيقونة
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0,0,0,0.1)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetY = 5;
            roundRect(ctx, 196, 196, 120, 120, 25);
            ctx.fill();
            ctx.shadowColor = 'transparent';
            
            // أيقونة الحاسبة
            const iconGradient = ctx.createLinearGradient(216, 216, 296, 296);
            iconGradient.addColorStop(0, '#667eea');
            iconGradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = iconGradient;
            roundRect(ctx, 216, 216, 80, 80, 15);
            ctx.fill();
            
            // شاشة الحاسبة
            ctx.fillStyle = '#2C5364';
            roundRect(ctx, 226, 226, 60, 20, 4);
            ctx.fill();
            
            // نص الشاشة
            ctx.fillStyle = '#00ff88';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('123,456', 256, 240);
            
            // أزرار الحاسبة
            ctx.fillStyle = 'white';
            for(let row = 0; row < 3; row++) {
                for(let col = 0; col < 3; col++) {
                    const x = 230 + col * 15;
                    const y = 255 + row * 12;
                    roundRect(ctx, x, y, 12, 8, 2);
                    ctx.fill();
                }
            }
            
            // عملة ذهبية
            const goldGradient = ctx.createRadialGradient(370, 170, 0, 370, 170, 30);
            goldGradient.addColorStop(0, '#FFD700');
            goldGradient.addColorStop(1, '#FFA500');
            
            ctx.fillStyle = goldGradient;
            ctx.beginPath();
            ctx.arc(370, 170, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // حدود بيضاء للعملة
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // رمز الدولار
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('$', 370, 178);
            
            // بطاقة ائتمان
            const cardGradient = ctx.createLinearGradient(120, 340, 170, 372);
            cardGradient.addColorStop(0, '#4CAF50');
            cardGradient.addColorStop(1, '#45a049');
            
            ctx.fillStyle = cardGradient;
            roundRect(ctx, 120, 340, 50, 32, 6);
            ctx.fill();
            
            // حدود بيضاء للبطاقة
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // خط مغناطيسي
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            roundRect(ctx, 126, 350, 38, 4, 1);
            ctx.fill();
            
            // نقاط البطاقة
            ctx.fillStyle = 'white';
            for(let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.arc(132 + i * 6, 362, 2, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // النص العربي
            ctx.fillStyle = 'white';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('محاسب ديون', 256, 420);
            
            ctx.font = '18px Arial';
            ctx.fillText('احترافي', 256, 450);
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // إنشاء الأيقونة عند تحميل الصفحة
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
