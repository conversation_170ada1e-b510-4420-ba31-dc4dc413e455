import 'package:flutter/material.dart';

/// مساعد تحسين أداء القوائم
class ListPerformanceHelper {
  /// إعدادات محسنة للقوائم الطويلة
  static const double optimizedItemExtent = 120.0;
  static const double optimizedCacheExtent = 400.0;
  static const bool addAutomaticKeepAlives = false;
  static const bool addRepaintBoundaries = true;
  static const bool addSemanticIndexes = false;

  /// فيزياء محسنة للتمرير
  static const ScrollPhysics optimizedScrollPhysics = BouncingScrollPhysics(
    parent: AlwaysScrollableScrollPhysics(),
  );

  /// إعدادات الرسوم المتحركة المحسنة
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration normalAnimationDuration = Duration(milliseconds: 200);

  /// بناء ListView محسن للأداء
  static Widget buildOptimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    bool shrinkWrap = false,
    double? itemExtent,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      padding: padding,
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: optimizedScrollPhysics,
      cacheExtent: optimizedCacheExtent,
      itemExtent: itemExtent ?? optimizedItemExtent,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
    );
  }

  /// بناء Container محسن للبطاقات
  static Widget buildOptimizedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    BorderRadius? borderRadius,
    double? elevation,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 8),
      child: Material(
        elevation: elevation ?? 1,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        shadowColor: Colors.grey.withValues(alpha: 0.1),
        child: Container(
          padding: padding ?? const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color ?? Colors.white,
            borderRadius: borderRadius ?? BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: child,
        ),
      ),
    );
  }

  /// بناء أفاتار محسن
  static Widget buildOptimizedAvatar({
    required String text,
    required Color color,
    double size = 50,
    double fontSize = 20,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// بناء زر محسن
  static Widget buildOptimizedButton({
    required VoidCallback onTap,
    required Widget child,
    Color? color,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color ?? Colors.blue,
          borderRadius: borderRadius ?? BorderRadius.circular(6),
        ),
        child: child,
      ),
    );
  }

  /// تحسين الذاكرة للقوائم الطويلة
  static void optimizeListMemory() {
    // تنظيف cache الصور
    PaintingBinding.instance.imageCache.clear();
    
    // تحديد حد أقصى للصور في الذاكرة
    PaintingBinding.instance.imageCache.maximumSize = 50;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 20 << 20; // 20 MB
  }

  /// مراقبة أداء القوائم
  static void monitorListPerformance(String listName) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      debugPrint('📊 $listName rendered at: ${now.millisecondsSinceEpoch}');
    });
  }

  /// تحسين إعادة البناء
  static Widget buildWithRepaintBoundary(Widget child) {
    return RepaintBoundary(child: child);
  }

  /// تحسين الرسوم المتحركة
  static Widget buildOptimizedAnimatedContainer({
    required Widget child,
    Duration? duration,
    Curve? curve,
  }) {
    return AnimatedContainer(
      duration: duration ?? fastAnimationDuration,
      curve: curve ?? Curves.easeOut,
      child: child,
    );
  }
}
