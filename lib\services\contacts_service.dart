import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';

class ContactsService {
  factory ContactsService() => _instance;
  ContactsService._internal();
  static final ContactsService _instance = ContactsService._internal();

  /// طلب إذن الوصول إلى جهات الاتصال
  Future<bool> requestContactsPermission() async {
    try {
      final permission = await Permission.contacts.request();
      return permission.isGranted;
    } catch (e) {
      debugPrint('خطأ في طلب إذن جهات الاتصال: $e');
      return false;
    }
  }

  /// التحقق من حالة إذن جهات الاتصال
  Future<bool> hasContactsPermission() async {
    try {
      final permission = await Permission.contacts.status;
      return permission.isGranted;
    } catch (e) {
      debugPrint('خطأ في التحقق من إذن جهات الاتصال: $e');
      return false;
    }
  }

  /// جلب جميع جهات الاتصال الحقيقية من الجهاز
  Future<List<ContactInfo>> getAllContacts() async {
    try {
      // التحقق من الإذن أولاً
      if (!await hasContactsPermission()) {
        final granted = await requestContactsPermission();
        if (!granted) {
          throw Exception('لم يتم منح إذن الوصول إلى جهات الاتصال');
        }
      }

      // جلب جهات الاتصال من الجهاز
      final deviceContacts = await FlutterContacts.getContacts(
        withProperties: true,
      );
      final List<ContactInfo> contactInfoList = [];

      for (final contact in deviceContacts) {
        // التأكد من وجود اسم ورقم هاتف
        if (contact.displayName.isNotEmpty && contact.phones.isNotEmpty) {
          // إضافة كل رقم هاتف كجهة اتصال منفصلة
          for (final phone in contact.phones) {
            if (phone.number.isNotEmpty) {
              contactInfoList.add(
                ContactInfo(
                  name: contact.displayName,
                  phoneNumber: formatPhoneNumber(phone.number),
                  originalPhoneNumber: phone.number,
                ),
              );
            }
          }
        }
      }

      // ترتيب جهات الاتصال أبجدياً
      contactInfoList.sort((a, b) => a.name.compareTo(b.name));

      return contactInfoList;
    } catch (e) {
      debugPrint('خطأ في جلب جهات الاتصال: $e');
      // في حالة الخطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// البحث في جهات الاتصال
  Future<List<ContactInfo>> searchContacts(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllContacts();
      }

      final allContacts = await getAllContacts();
      final lowerQuery = query.toLowerCase();

      return allContacts.where((contact) {
        final name = contact.name.toLowerCase();
        final phone = contact.phoneNumber;

        return name.contains(lowerQuery) ||
            phone.contains(query.replaceAll(' ', ''));
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في جهات الاتصال: $e');
      return [];
    }
  }

  /// تنسيق رقم الهاتف
  String formatPhoneNumber(String phoneNumber) {
    // إزالة المسافات والرموز الخاصة
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // إزالة رمز البلد إذا كان موجوداً
    if (cleaned.startsWith('+964')) {
      cleaned = cleaned.substring(4);
    } else if (cleaned.startsWith('964')) {
      cleaned = cleaned.substring(3);
    }

    // إضافة 0 في البداية إذا لم تكن موجودة
    if (!cleaned.startsWith('0') && cleaned.length == 10) {
      cleaned = '0$cleaned';
    }

    return cleaned;
  }

  /// التحقق من صحة رقم الهاتف العراقي
  bool isValidIraqiPhoneNumber(String phoneNumber) {
    final formatted = formatPhoneNumber(phoneNumber);

    // رقم الهاتف العراقي يجب أن يكون 11 رقم ويبدأ بـ 07
    return formatted.length == 11 && formatted.startsWith('07');
  }
}

/// نموذج بيانات جهة الاتصال المبسط
class ContactInfo {
  ContactInfo({
    required this.name,
    required this.phoneNumber,
    this.originalPhoneNumber,
  });

  final String name;
  final String phoneNumber;
  final String? originalPhoneNumber;

  @override
  String toString() {
    return 'ContactInfo(name: $name, phoneNumber: $phoneNumber)';
  }
}
