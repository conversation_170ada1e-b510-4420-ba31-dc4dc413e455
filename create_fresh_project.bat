@echo off
echo ========================================
echo    إنشاء مشروع جديد نظيف
echo ========================================
echo.

echo 📁 إنشاء نسخة احتياطية...
if not exist "backup" mkdir backup
xcopy lib backup\lib /E /I /Y >nul 2>&1
xcopy assets backup\assets /E /I /Y >nul 2>&1
copy pubspec.yaml backup\ >nul 2>&1

echo.
echo 🆕 إنشاء مشروع Flutter جديد...
cd ..
flutter create mahasb_new

echo.
echo 📋 نقل الملفات للمشروع الجديد...
cd mahasb_new
rmdir /s /q lib
rmdir /s /q assets >nul 2>&1

xcopy ..\mahasb\backup\lib lib /E /I /Y
xcopy ..\mahasb\backup\assets assets /E /I /Y >nul 2>&1
copy ..\mahasb\backup\pubspec.yaml . /Y

echo.
echo 🔧 تحديث التبعيات...
flutter pub get

echo.
echo 🚀 تشغيل المشروع الجديد...
flutter run -d chrome --debug

echo.
echo ========================================
echo    تم إنشاء مشروع جديد نظيف!
echo    المجلد: mahasb_new
echo ========================================
pause
