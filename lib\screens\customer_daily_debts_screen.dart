import 'package:flutter/material.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../widgets/debt_card.dart';
import '../database/database_helper.dart';

class CustomerDailyDebtsScreen extends StatefulWidget {
  const CustomerDailyDebtsScreen({
    super.key,
    required this.customer,
    required this.isToday,
    required this.title,
  });

  final Customer customer;
  final bool isToday;
  final String title;

  @override
  State<CustomerDailyDebtsScreen> createState() =>
      _CustomerDailyDebtsScreenState();
}

class _CustomerDailyDebtsScreenState extends State<CustomerDailyDebtsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Debt> _debts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDebts();
  }

  Future<void> _loadDebts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب جميع ديون العميل
      final allDebts = await _databaseHelper.getAllDebts();
      final customerDebts = allDebts
          .where((debt) => debt.customerId == widget.customer.id)
          .toList();

      final now = DateTime.now();
      final targetDate = widget.isToday
          ? DateTime(now.year, now.month, now.day)
          : DateTime(
              now.year,
              now.month,
              now.day,
            ).subtract(const Duration(days: 1));

      // فلترة الديون حسب التاريخ
      final filteredDebts = customerDebts.where((debt) {
        final debtDay = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );
        return debtDay.isAtSameMomentAs(targetDate);
      }).toList();

      setState(() {
        _debts = filteredDebts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.title} - ${widget.customer.name}'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _debts.isEmpty
          ? _buildEmptyState()
          : _buildDebtsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            widget.isToday
                ? 'لا توجد ديون اليوم لهذا العميل'
                : 'لا توجد ديون الأمس لهذا العميل',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم إضافة أي ديون لهذا العميل في هذا التاريخ',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDebtsList() {
    return RefreshIndicator(
      onRefresh: _loadDebts,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _debts.length,
        itemBuilder: (context, index) {
          final debt = _debts[index];

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: DebtCard(debt: debt),
          );
        },
      ),
    );
  }
}
