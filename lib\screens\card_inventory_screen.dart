import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/card_type_provider.dart';
import '../models/card_inventory.dart';

class CardInventoryScreen extends StatefulWidget {
  const CardInventoryScreen({super.key});

  @override
  State<CardInventoryScreen> createState() => _CardInventoryScreenState();
}

class _CardInventoryScreenState extends State<CardInventoryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      ).loadInventories();
      Provider.of<CardTypeProvider>(
        context,
        listen: false,
      ).loadCustomCardTypes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إدارة الكميات',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showInventoryAnalytics(context),
            tooltip: 'تحليل المخزون',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<CardInventoryProvider>(
                context,
                listen: false,
              ).loadInventories();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Consumer<CardInventoryProvider>(
        builder: (context, inventoryProvider, child) {
          if (inventoryProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (inventoryProvider.inventories.isEmpty) {
            return _buildEmptyState();
          }

          return _buildInventoryList(inventoryProvider);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddStockDialog(context),
        backgroundColor: Colors.blue.shade600,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد كميات مسجلة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة كمية جديدة',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryList(CardInventoryProvider inventoryProvider) {
    return Column(
      children: [
        // إجمالي البطاقات
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade600, Colors.blue.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(Icons.credit_card, color: Colors.white, size: 32),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إجمالي الكارتات',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${inventoryProvider.getTotalCardsCount()} كارت',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${inventoryProvider.inventories.length} نوع كارت',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 6),
                    // عرض أنواع الكارتات مع ألوانها في صف واحد
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: inventoryProvider.inventories.map((
                          inventory,
                        ) {
                          return Container(
                            margin: const EdgeInsets.only(right: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 3,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 0.5,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: _getCardTypeColor(
                                      inventory.cardType,
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 3),
                                Text(
                                  _translateCardType(inventory.cardType),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 9,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${inventory.quantity}',
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // قائمة المخزون
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: inventoryProvider.inventories.length,
            itemBuilder: (context, index) {
              final inventory = inventoryProvider.inventories[index];
              return _buildInventoryCard(inventory, inventoryProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryCard(
    CardInventory inventory,
    CardInventoryProvider inventoryProvider,
  ) {
    // حساب عدد الأيام منذ الإضافة
    final daysSinceAdded =
        DateTime.now().difference(inventory.createdAt).inDays;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getCardTypeColor(inventory.cardType),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _translateCardType(inventory.cardType),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                // عداد الأيام
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Text(
                    'منذ شراء الكمية $daysSinceAdded يوم',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                _buildStatusIndicator(inventory),
              ],
            ),
            const SizedBox(height: 8),
            // تاريخ الشراء واسم اليوم والوقت
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ الشراء: ',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      _formatDateTime(inventory.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'الكمية المتوفرة',
                    '${inventory.quantity} بطاقة',
                  ),
                ),
                const SizedBox(width: 8), // فاصل بين البطاقات
                Expanded(
                  child: _buildInfoItem(
                    'الحد الأدنى',
                    '${inventory.minQuantity} بطاقة',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => _showEditPriceDialog(context, inventory),
                    child: _buildInfoItem(
                      'السعر للوحدة',
                      '${_formatCurrency(inventory.price)} ألف',
                      isEditable: true,
                    ),
                  ),
                ),
                const SizedBox(width: 8), // فاصل بين البطاقات
                Expanded(
                  child: _buildInfoItem(
                    'الإجمالي',
                    '${_formatCurrency(inventory.totalValue)} ألف',
                    isTotal: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        _showAddStockDialog(context, inventory.cardType),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('إضافة كمية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showEditInventoryDialog(context, inventory),
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  onPressed: () => _showDeleteConfirmation(
                    context,
                    inventory,
                    inventoryProvider,
                  ),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(CardInventory inventory) {
    if (inventory.isOutOfStock) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'نافد',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    } else if (inventory.isLowStock) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'منخفض',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'متوفر',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    }
  }

  Widget _buildInfoItem(String label, String value,
      {bool isEditable = false, bool isTotal = false}) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50, // لون موحد لجميع البطاقات
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200, // حدود موحدة لجميع البطاقات
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              if (isEditable) ...[
                const SizedBox(width: 4),
                Icon(
                  Icons.edit,
                  size: 12,
                  color: Colors.blue.shade600,
                ),
              ],
            ],
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87, // لون نص موحد
            ),
          ),
        ],
      ),
    );
  }

  Color _getCardTypeColor(String cardType) {
    final translatedType = _translateCardType(cardType);
    switch (translatedType) {
      case 'زين':
        return Colors.purple; // بنفسجي
      case 'آسيا':
        return Colors.red; // أحمر
      case 'أبو الستة':
        return Colors.grey.shade600; // أبيض (رمادي للوضوح)
      case 'أبو العشرة':
        return Colors.cyan.shade300; // فيروزي فاتح
      case 'نقدي':
        return Colors.green; // أخضر للنقدي
      default:
        return Colors.blue; // لون افتراضي
    }
  }

  // الحصول على اسم الكارت الصحيح من قاعدة البيانات أو الترجمة
  String _translateCardType(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (_isArabicText(cardType)) {
      return cardType.trim();
    }

    // محاولة الحصول على الاسم من CardTypeProvider أولاً
    try {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );

      // البحث بالمعرف المباشر
      final cardTypeOption = cardTypeProvider.getCardTypeById(cardType);
      if (cardTypeOption != null) {
        return cardTypeOption.displayName;
      }

      // البحث في الأنواع المخصصة
      final customCardType = cardTypeProvider.customCardTypes
          .where((ct) => ct.name == cardType || ct.displayName == cardType)
          .firstOrNull;
      if (customCardType != null) {
        return customCardType.displayName;
      }
    } catch (e) {
      debugPrint('Error getting card type from provider: $e');
    }

    // إذا لم نجد في Provider، نحاول الترجمة اليدوية
    return _fallbackTranslateCardType(cardType);
  }

  // ترجمة احتياطية للأنواع المعروفة
  String _fallbackTranslateCardType(String cardType) {
    // تنظيف النص مع الاحتفاظ بالأرقام للأنواع المخصصة
    final originalLower = cardType.trim().toLowerCase();

    // إذا كان النوع مخصص بأرقام (مثل Custom_3, Custom_5)
    if (originalLower.startsWith('custom_') &&
        RegExp(r'custom_\d+$').hasMatch(originalLower)) {
      final number = originalLower.replaceAll('custom_', '');
      return 'نوع مخصص $number';
    }

    final cleanType = cardType
        .trim()
        .toLowerCase()
        .replaceAll(
          RegExp(r'[^a-zA-Z\u0600-\u06FF]'),
          '',
        ) // إزالة كل شيء عدا الحروف
        .replaceAll('custom', '') // إزالة كلمة custom
        .trim();

    // البحث الذكي بالكلمات المفتاحية - حل جذري وشامل

    // زين - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'zain',
      'زين',
      'zaincard',
      'zaincash',
      'zainmoney',
      'zainiraq',
      'zaintelecom',
      'zaintel',
      'zn',
      'zain5000',
      'zain10000',
    ])) {
      return 'زين';
    }

    // آسيا - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'asia',
      'sia',
      'آسيا',
      'اسيا',
      'asiacard',
      'siacard',
      'asiacash',
      'siacash',
      'asiacell',
      'siacell',
      'asiatelecom',
      'siatelecom',
      'asiacel',
      'siacel',
      'asia5000',
      'sia5000',
      'asia10000',
      'sia10000',
    ])) {
      return 'آسيا';
    }

    // أبو العشرة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abuashara',
      'ashara',
      'abuashara',
      'abuasharacard',
      'asharacard',
      'abuasharacash',
      'asharacash',
      'أبوالعشرة',
      'ابوالعشرة',
      'أبوعشرة',
      'ابوعشرة',
      'العشرة',
      'عشرة',
    ])) {
      return 'أبو العشرة';
    }

    // أبو الستة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abusitta',
      'sitta',
      'abusitta',
      'abusittacard',
      'sittacard',
      'abusittacash',
      'sittacash',
      'أبوالستة',
      'ابوالستة',
      'أبوستة',
      'ابوستة',
      'الستة',
      'ستة',
    ])) {
      return 'أبو الستة';
    }

    // الأنواع الأساسية
    if (_containsAny(cleanType, ['cash', 'نقدي'])) {
      return 'نقدي';
    }

    if (_containsAny(cleanType, ['visa', 'فيزا'])) {
      return 'فيزا';
    }

    if (_containsAny(cleanType, ['mastercard', 'master', 'ماستر'])) {
      return 'ماستركارد';
    }

    if (_containsAny(cleanType, [
      'americanexpress',
      'american',
      'express',
      'امريكان',
    ])) {
      return 'أمريكان إكسبريس';
    }

    if (_containsAny(cleanType, ['other', 'اخرى', 'أخرى'])) {
      return 'أخرى';
    }

    // إذا لم نجد مطابقة، نعيد الاسم الأصلي مع تحسين التنسيق
    return _capitalizeFirst(cardType.trim());
  }

  // دالة مساعدة للبحث في قائمة من الكلمات المفتاحية - محسنة
  bool _containsAny(String text, List<String> keywords) {
    final lowerText = text.toLowerCase();
    for (final keyword in keywords) {
      final lowerKeyword = keyword.toLowerCase();
      // بحث دقيق ومرن
      if (lowerText.contains(lowerKeyword) ||
          lowerText == lowerKeyword ||
          lowerText.startsWith(lowerKeyword) ||
          lowerText.endsWith(lowerKeyword)) {
        return true;
      }
    }
    return false;
  }

  // تحسين تنسيق النص
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // فحص إذا كان النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  void _showAddStockDialog(BuildContext context, [String? cardType]) {
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );
    String? selectedCardType = cardType;
    final quantityController = TextEditingController();
    final minQuantityController = TextEditingController(text: '10');
    final priceController = TextEditingController();

    // تعيين السعر الافتراضي عند فتح النافذة
    if (selectedCardType != null) {
      final defaultPrice = _getDefaultPrice(selectedCardType);
      priceController.text = _formatCurrency(defaultPrice);
    }

    // تحديث السعر الافتراضي عند تغيير نوع الكارت
    void updateDefaultPrice() {
      if (selectedCardType != null) {
        final defaultPrice = _getDefaultPrice(selectedCardType!);
        priceController.text = _formatCurrency(defaultPrice);
      }
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_box,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  cardType != null
                      ? 'إضافة كمية لـ ${_translateCardType(cardType)}'
                      : 'إضافة كمية جديدة',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (cardType == null) ...[
                  // اختيار نوع البطاقة
                  DropdownButtonFormField<String>(
                    value: selectedCardType,
                    decoration: InputDecoration(
                      labelText: 'نوع البطاقة',
                      labelStyle: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.blue.shade600,
                          width: 2,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      prefixIcon: Icon(
                        Icons.credit_card,
                        color: Colors.blue.shade600,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                    ),
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    dropdownColor: Colors.white,
                    items: _getUniqueCardTypes(cardTypeProvider),
                    onChanged: (value) {
                      setState(() {
                        selectedCardType = value;
                      });
                      updateDefaultPrice();
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // الكمية المضافة
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    labelText: 'الكمية المضافة',
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.green.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon: Icon(Icons.add, color: Colors.green.shade600),
                    suffixText: 'بطاقة',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
                const SizedBox(height: 16),

                // السعر
                TextField(
                  controller: priceController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                    ThousandsSeparatorInputFormatter(),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  onTap: () {
                    // تحديد النص بالكامل عند التركيز لتسهيل الإدخال
                    if (priceController.text.isNotEmpty) {
                      priceController.selection = TextSelection(
                        baseOffset: 0,
                        extentOffset: priceController.text.length,
                      );
                    }
                  },
                  decoration: InputDecoration(
                    labelText: 'السعر للوحدة (بالآلاف)',
                    hintText: 'أدخل السعر',
                    hintStyle: TextStyle(color: Colors.grey.shade400),
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.blue.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon:
                        Icon(Icons.attach_money, color: Colors.blue.shade600),
                    suffixText: 'ألف',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
                const SizedBox(height: 16),

                // الحد الأدنى
                TextField(
                  controller: minQuantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    labelText: 'الحد الأدنى للتنبيه',
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.orange.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon: Icon(
                      Icons.warning,
                      color: Colors.orange.shade600,
                    ),
                    suffixText: 'بطاقة',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade700,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedCardType != null &&
                    quantityController.text.isNotEmpty) {
                  try {
                    final quantity = int.parse(quantityController.text);
                    final price = double.tryParse(
                            priceController.text.replaceAll(',', '')) ??
                        0.0;

                    final inventoryProvider =
                        Provider.of<CardInventoryProvider>(
                      context,
                      listen: false,
                    );
                    await inventoryProvider.addStock(
                      selectedCardType!,
                      quantity,
                      price: price,
                    );

                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'تم إضافة $quantity بطاقة من ${_translateCardType(selectedCardType!)} بسعر ${_formatCurrency(price)} ألف',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'إضافة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<DropdownMenuItem<String>> _getUniqueCardTypes(
    CardTypeProvider cardTypeProvider,
  ) {
    final cardTypes = <String>{};

    try {
      for (final cardType in cardTypeProvider.allCardTypes) {
        if (cardType.displayName.isNotEmpty) {
          cardTypes.add(cardType.displayName);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع البطاقات: $e');
    }

    final sortedTypes = cardTypes.toList()..sort();

    return sortedTypes.map((cardType) {
      return DropdownMenuItem<String>(
        value: cardType,
        child: Text(cardType, style: const TextStyle(fontSize: 16)),
      );
    }).toList();
  }

  void _showEditInventoryDialog(BuildContext context, CardInventory inventory) {
    final quantityController = TextEditingController(
      text: inventory.quantity.toString(),
    );
    final minQuantityController = TextEditingController(
      text: inventory.minQuantity.toString(),
    );
    final priceController = TextEditingController(
      text: inventory.price.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit, color: Colors.orange.shade600, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'تعديل ${_translateCardType(inventory.cardType)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الكمية الحالية
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'الكمية الحالية',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.orange.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon: Icon(
                  Icons.inventory,
                  color: Colors.orange.shade600,
                ),
                suffixText: 'بطاقة',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
            const SizedBox(height: 16),

            // السعر
            TextField(
              controller: priceController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'السعر للوحدة (بالآلاف)',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.blue.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon:
                    Icon(Icons.attach_money, color: Colors.blue.shade600),
                suffixText: 'ألف',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              onChanged: (value) {
                // إضافة فاصل الآلاف
                if (value.isNotEmpty) {
                  final numericValue = value.replaceAll(',', '');
                  if (double.tryParse(numericValue) != null) {
                    final formatted =
                        _formatCurrency(double.parse(numericValue));
                    if (formatted != value) {
                      priceController.value = TextEditingValue(
                        text: formatted,
                        selection:
                            TextSelection.collapsed(offset: formatted.length),
                      );
                    }
                  }
                }
              },
            ),
            const SizedBox(height: 16),

            // الحد الأدنى
            TextField(
              controller: minQuantityController,
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'الحد الأدنى للتنبيه',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade600, width: 2),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon: Icon(Icons.warning, color: Colors.red.shade600),
                suffixText: 'بطاقة',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (quantityController.text.isNotEmpty &&
                  minQuantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty) {
                try {
                  final quantity = int.parse(quantityController.text);
                  final minQuantity = int.parse(minQuantityController.text);
                  final price =
                      double.parse(priceController.text.replaceAll(',', ''));

                  final updatedInventory = inventory.copyWith(
                    quantity: quantity,
                    minQuantity: minQuantity,
                    price: price,
                    updatedAt: DateTime.now(),
                  );

                  final inventoryProvider = Provider.of<CardInventoryProvider>(
                    context,
                    listen: false,
                  );
                  await inventoryProvider.updateInventory(updatedInventory);

                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم تحديث ${inventory.cardType} بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'تحديث',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    CardInventory inventory,
    CardInventoryProvider inventoryProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red.shade600),
            const SizedBox(width: 8),
            const Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'هل أنت متأكد من حذف ${inventory.cardType}؟',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Column(
                children: [
                  Text(
                    'الكمية الحالية: ${inventory.quantity} بطاقة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'سيتم حذف جميع البيانات المتعلقة بهذا النوع',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await inventoryProvider.deleteInventory(inventory.id!);

                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف ${inventory.cardType} بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showInventoryAnalytics(BuildContext context) {
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );
    final lowStockItems = inventoryProvider.getLowStockItems();
    final outOfStockItems = inventoryProvider.getOutOfStockItems();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('تحليل المخزون'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إجمالي الكارتات
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade50, Colors.blue.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.credit_card,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إجمالي الكارتات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${inventoryProvider.getTotalCardsCount()} كارت',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            '${inventoryProvider.inventories.length} نوع كارت مختلف',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          // عرض أنواع الكارتات مع ألوانها
                          Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: inventoryProvider.inventories.map((
                              inventory,
                            ) {
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 3,
                                ),
                                decoration: BoxDecoration(
                                  color: _getCardTypeColor(
                                    inventory.cardType,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: _getCardTypeColor(
                                      inventory.cardType,
                                    ).withValues(alpha: 0.3),
                                    width: 0.5,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 6,
                                      height: 6,
                                      decoration: BoxDecoration(
                                        color: _getCardTypeColor(
                                          inventory.cardType,
                                        ),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      inventory.cardType,
                                      style: TextStyle(
                                        color: _getCardTypeColor(
                                          inventory.cardType,
                                        ),
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      '(${inventory.quantity})',
                                      style: TextStyle(
                                        color: _getCardTypeColor(
                                          inventory.cardType,
                                        ),
                                        fontSize: 9,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // الكارتات النافدة
              if (outOfStockItems.isNotEmpty) ...[
                Text(
                  'الكارتات النافدة (${outOfStockItems.length}):',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...outOfStockItems.map(
                  (item) => Container(
                    margin: const EdgeInsets.only(bottom: 4),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red.shade600, size: 16),
                        const SizedBox(width: 8),
                        Text(item.cardType),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // الكارتات منخفضة المخزون
              if (lowStockItems.isNotEmpty) ...[
                Text(
                  'الكارتات منخفضة المخزون (${lowStockItems.length}):',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...lowStockItems.map(
                  (item) => Container(
                    margin: const EdgeInsets.only(bottom: 4),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: Colors.orange.shade600,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(item.cardType)),
                        Text(
                          '${item.quantity} كارت',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // إحصائيات عامة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.analytics,
                          color: Colors.grey.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'إحصائيات عامة',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildStatRow(
                      'إجمالي أنواع الكارتات',
                      '${inventoryProvider.inventories.length}',
                      Colors.blue,
                    ),
                    _buildStatRow(
                      'الكارتات النافدة',
                      '${outOfStockItems.length}',
                      Colors.red,
                    ),
                    _buildStatRow(
                      'الكارتات منخفضة المخزون',
                      '${lowStockItems.length}',
                      Colors.orange,
                    ),
                    _buildStatRow(
                      'الكارتات المتوفرة',
                      '${inventoryProvider.inventories.length - outOfStockItems.length - lowStockItems.length}',
                      Colors.green,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // دالة تنسيق تاريخ الشراء واسم اليوم والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // أسماء الأيام بالعربية
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    // أسماء الأشهر بالترقيم العربي
    const monthNames = [
      'الأول',
      'الثاني',
      'الثالث',
      'الرابع',
      'الخامس',
      'السادس',
      'السابع',
      'الثامن',
      'التاسع',
      'العاشر',
      'الحادي عشر',
      'الثاني عشر',
    ];

    final dayName = dayNames[dateTime.weekday - 1];
    final monthName = monthNames[dateTime.month - 1];

    // تنسيق الوقت (12 ساعة)
    final hour = dateTime.hour == 0
        ? 12
        : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final period = dateTime.hour >= 12 ? 'مساءً' : 'صباحاً';
    final minute = dateTime.minute.toString().padLeft(2, '0');

    // تنسيق التاريخ بالشكل المطلوب: 2025\6\3 شهر السادس
    final dateFormat =
        '${dateTime.year}\\${dateTime.month}\\${dateTime.day} شهر $monthName';

    if (difference.inDays == 0) {
      return 'اليوم ($dayName) - $dateFormat - الساعة $hour:$minute $period';
    } else if (difference.inDays == 1) {
      return 'أمس - $dateFormat - الساعة $hour:$minute $period';
    } else {
      return '$dayName - $dateFormat - الساعة $hour:$minute $period';
    }
  }

  // دالة بناء صف الإحصائيات
  Widget _buildStatRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // دالة تنسيق العملة
  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(3).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  // دالة عرض نافذة تعديل السعر
  void _showEditPriceDialog(BuildContext context, CardInventory inventory) {
    final priceController =
        TextEditingController(text: inventory.price.toString());

    // متغير لتحديث الإجمالي
    final ValueNotifier<double> totalNotifier =
        ValueNotifier(inventory.totalValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.attach_money,
                color: Colors.blue.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'تعديل سعر ${_translateCardType(inventory.cardType)}',
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: priceController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                labelText: 'السعر (بالآلاف)',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.blue.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon:
                    Icon(Icons.attach_money, color: Colors.blue.shade600),
                suffixText: 'ألف',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              onChanged: (value) {
                final price = double.tryParse(value) ?? 0.0;
                totalNotifier.value = price * inventory.quantity;
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      color: Colors.green.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ValueListenableBuilder<double>(
                      valueListenable: totalNotifier,
                      builder: (context, total, child) {
                        return Text(
                          'الإجمالي سيكون: ${_formatCurrency(total)} ألف',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (priceController.text.isNotEmpty) {
                try {
                  final price =
                      double.parse(priceController.text.replaceAll(',', ''));

                  final updatedInventory = inventory.copyWith(
                    price: price,
                    updatedAt: DateTime.now(),
                  );

                  final inventoryProvider = Provider.of<CardInventoryProvider>(
                    context,
                    listen: false,
                  );
                  await inventoryProvider.updateInventory(updatedInventory);

                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content:
                            Text('تم تحديث سعر ${inventory.cardType} بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // دالة للحصول على السعر الافتراضي حسب نوع الكارت
  double _getDefaultPrice(String cardType) {
    final translatedType = _translateCardType(cardType);
    switch (translatedType) {
      case 'زين':
        return 6.750;
      case 'آسيا':
        return 7.750;
      case 'أبو العشرة':
        return 9.800;
      case 'أبو الستة':
        return 4.800;
      case 'نقدي':
        return 0.0;
      default:
        return 5.000;
    }
  }
}

// TextInputFormatter لفاصل الآلاف
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // إذا كان النص فارغ، إرجاع القيمة كما هي
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // إزالة جميع الفواصل الموجودة
    final String cleanText = newValue.text.replaceAll(',', '');

    // التحقق من صحة الرقم
    if (double.tryParse(cleanText) == null) {
      return oldValue;
    }

    // إضافة فواصل الآلاف
    final String formattedText = _addThousandsSeparator(cleanText);

    // حساب موضع المؤشر الجديد
    int cursorPosition = newValue.selection.baseOffset;

    // إذا كان المؤشر في نهاية النص، ضعه في نهاية النص المنسق
    if (cursorPosition >= newValue.text.length) {
      cursorPosition = formattedText.length;
    } else {
      // حساب عدد الفواصل المضافة قبل موضع المؤشر
      final String textBeforeCursor =
          newValue.text.substring(0, cursorPosition);
      final String cleanTextBeforeCursor = textBeforeCursor.replaceAll(',', '');
      final String formattedTextBeforeCursor =
          _addThousandsSeparator(cleanTextBeforeCursor);
      cursorPosition = formattedTextBeforeCursor.length;
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(
          offset: cursorPosition.clamp(0, formattedText.length)),
    );
  }

  String _addThousandsSeparator(String value) {
    if (value.isEmpty) return value;

    // فصل الجزء الصحيح عن الجزء العشري
    final List<String> parts = value.split('.');
    final String integerPart = parts[0];
    final String decimalPart = parts.length > 1 ? parts[1] : '';

    // إضافة فواصل الآلاف للجزء الصحيح
    final String formattedInteger = integerPart.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );

    // إعادة تجميع الرقم
    if (decimalPart.isNotEmpty) {
      return '$formattedInteger.$decimalPart';
    } else {
      return formattedInteger;
    }
  }
}
