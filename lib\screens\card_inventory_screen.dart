import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/card_type_provider.dart';
import '../models/card_inventory.dart';

class CardInventoryScreen extends StatefulWidget {
  const CardInventoryScreen({super.key});

  @override
  State<CardInventoryScreen> createState() => _CardInventoryScreenState();
}

class _CardInventoryScreenState extends State<CardInventoryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      ).loadInventories();
      Provider.of<CardTypeProvider>(
        context,
        listen: false,
      ).loadCustomCardTypes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إدارة الكميات',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<CardInventoryProvider>(
                context,
                listen: false,
              ).loadInventories();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Consumer<CardInventoryProvider>(
        builder: (context, inventoryProvider, child) {
          if (inventoryProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (inventoryProvider.inventories.isEmpty) {
            return _buildEmptyState();
          }

          return _buildInventoryList(inventoryProvider);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddStockDialog(context),
        backgroundColor: Colors.blue.shade600,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد كميات مسجلة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة كمية جديدة',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryList(CardInventoryProvider inventoryProvider) {
    return Column(
      children: [
        // بطاقة الإحصائيات والتحليل القابلة للطي
        _buildStatisticsCard(inventoryProvider),

        // قائمة المخزون
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.fromLTRB(
                16, 0, 16, 100), // إضافة مساحة في الأسفل للزر العائم
            itemCount: inventoryProvider.inventories.length,
            itemBuilder: (context, index) {
              final inventory = inventoryProvider.inventories[index];
              return _buildInventoryCard(inventory, inventoryProvider);
            },
          ),
        ),
      ],
    );
  }

  // بطاقة الإحصائيات والتحليل القابلة للطي
  Widget _buildStatisticsCard(CardInventoryProvider inventoryProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ExpansionTile(
        backgroundColor: Colors.white,
        collapsedBackgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.analytics,
            color: Colors.grey.shade700,
            size: 24,
          ),
        ),
        title: Text(
          'الإحصائيات والتحليل',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        subtitle: Text(
          '${inventoryProvider.getTotalCardsCount()} كارت • ${inventoryProvider.inventories.length} نوع',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        children: [
          _buildStatisticsContent(inventoryProvider),
        ],
      ),
    );
  }

  // محتوى الإحصائيات المفصل
  Widget _buildStatisticsContent(CardInventoryProvider inventoryProvider) {
    // حساب إجمالي المبلغ
    double totalAmount = 0;
    for (final inventory in inventoryProvider.inventories) {
      totalAmount += inventory.totalValue;
    }

    final lowStockItems = inventoryProvider.getLowStockItems();
    final outOfStockItems = inventoryProvider.getOutOfStockItems();

    return Column(
      children: [
        // بطاقات الإحصائيات الرئيسية
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الكارتات',
                '${inventoryProvider.getTotalCardsCount()}',
                '',
                Icons.credit_card,
                Colors.blue.shade600,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'أنواع الكروت',
                '${inventoryProvider.inventories.length}',
                '',
                Icons.category,
                Colors.purple.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // بطاقة المبلغ الإجمالي - عريضة ومتوسطة
        _buildAmountCard(
          'إجمالي المبلغ',
          _formatCurrency(totalAmount),
          'ألف',
          Icons.attach_money,
          Colors.green.shade600,
        ),
        const SizedBox(height: 16),

        // تحليل المخزون
        if (lowStockItems.isNotEmpty || outOfStockItems.isNotEmpty) ...[
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              'تحليل المخزون',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ),
          const SizedBox(height: 12),
          if (outOfStockItems.isNotEmpty) ...[
            _buildAnalysisCard(
              'كروت نافدة',
              outOfStockItems.length.toString(),
              'نوع',
              Colors.red,
              Icons.warning,
            ),
            const SizedBox(height: 8),
          ],
          if (lowStockItems.isNotEmpty) ...[
            _buildAnalysisCard(
              'كروت منخفضة',
              lowStockItems.length.toString(),
              'نوع',
              Colors.orange,
              Icons.trending_down,
            ),
            const SizedBox(height: 8),
          ],
        ],

        // عرض أنواع الكروت
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: Text(
            'أنواع الكروت',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: inventoryProvider.inventories.map((inventory) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getCardTypeColor(inventory.cardType),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _translateCardType(inventory.cardType),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${inventory.quantity}',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // بطاقة إحصائية صغيرة - تصميم مركزي
  Widget _buildStatCard(
      String title, String value, String unit, IconData icon, Color iconColor) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // العنوان والأيقونة في الأعلى
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 20,
                color: iconColor,
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // القيمة في الوسط
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
            textAlign: TextAlign.center,
          ),
          // الوحدة تحت القيمة (إذا كانت موجودة)
          if (unit.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              unit,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  // بطاقة المبلغ الإجمالي - تصميم خاص
  Widget _buildAmountCard(
      String title, String value, String unit, IconData icon, Color iconColor) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // العنوان والأيقونة في الأعلى
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 24,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // القيمة والوحدة في صف واحد
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بطاقة تحليل المخزون
  Widget _buildAnalysisCard(
      String title, String value, String unit, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$value $unit',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryCard(
    CardInventory inventory,
    CardInventoryProvider inventoryProvider,
  ) {
    // حساب عدد الأيام منذ الإضافة
    final daysSinceAdded =
        DateTime.now().difference(inventory.createdAt).inDays;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getCardTypeColor(inventory.cardType),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _translateCardType(inventory.cardType),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                _buildStatusIndicator(inventory),
              ],
            ),
            const SizedBox(height: 8),
            // تاريخ الشراء واسم اليوم والوقت
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ الشراء: ',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      _formatDateTime(inventory.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'الكمية المتوفرة',
                    '${inventory.quantity} بطاقة',
                  ),
                ),
                const SizedBox(width: 8), // فاصل بين البطاقات
                Expanded(
                  child: _buildInfoItem(
                    'الحد الأدنى',
                    '${inventory.minQuantity} بطاقة',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => _showEditPriceDialog(context, inventory),
                    child: _buildInfoItem(
                      'السعر للوحدة',
                      '${_formatCurrency(inventory.price)} ألف',
                      isEditable: true,
                    ),
                  ),
                ),
                const SizedBox(width: 8), // فاصل بين البطاقات
                Expanded(
                  child: _buildInfoItem(
                    'الإجمالي',
                    '${_formatCurrency(inventory.totalValue)} ألف',
                    isTotal: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // بطاقة العداد المحسنة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'منذ شراء الكمية: ',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  Text(
                    daysSinceAdded == 0
                        ? 'اليوم'
                        : daysSinceAdded == 1
                            ? 'أمس'
                            : '$daysSinceAdded يوم',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () => _showEditInventoryDialog(context, inventory),
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  onPressed: () => _showDeleteConfirmation(
                    context,
                    inventory,
                    inventoryProvider,
                  ),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(CardInventory inventory) {
    if (inventory.isOutOfStock) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'نافد',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    } else if (inventory.isLowStock) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'منخفض',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'متوفر',
          style: TextStyle(color: Colors.white, fontSize: 10),
        ),
      );
    }
  }

  Widget _buildInfoItem(String label, String value,
      {bool isEditable = false, bool isTotal = false}) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50, // لون موحد لجميع البطاقات
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200, // حدود موحدة لجميع البطاقات
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              if (isEditable) ...[
                const SizedBox(width: 4),
                Icon(
                  Icons.edit,
                  size: 12,
                  color: Colors.blue.shade600,
                ),
              ],
            ],
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87, // لون نص موحد
            ),
          ),
        ],
      ),
    );
  }

  Color _getCardTypeColor(String cardType) {
    final translatedType = _translateCardType(cardType);
    switch (translatedType) {
      case 'زين':
        return Colors.purple; // بنفسجي
      case 'آسيا':
        return Colors.red; // أحمر
      case 'أبو الستة':
        return Colors.grey.shade600; // أبيض (رمادي للوضوح)
      case 'أبو العشرة':
        return Colors.cyan.shade300; // فيروزي فاتح
      case 'نقدي':
        return Colors.green; // أخضر للنقدي
      default:
        return Colors.blue; // لون افتراضي
    }
  }

  // الحصول على اسم الكارت الصحيح من قاعدة البيانات أو الترجمة
  String _translateCardType(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (_isArabicText(cardType)) {
      return cardType.trim();
    }

    // محاولة الحصول على الاسم من CardTypeProvider أولاً
    try {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );

      // البحث بالمعرف المباشر
      final cardTypeOption = cardTypeProvider.getCardTypeById(cardType);
      if (cardTypeOption != null) {
        return cardTypeOption.displayName;
      }

      // البحث في الأنواع المخصصة
      final customCardType = cardTypeProvider.customCardTypes
          .where((ct) => ct.name == cardType || ct.displayName == cardType)
          .firstOrNull;
      if (customCardType != null) {
        return customCardType.displayName;
      }
    } catch (e) {
      debugPrint('Error getting card type from provider: $e');
    }

    // إذا لم نجد في Provider، نحاول الترجمة اليدوية
    return _fallbackTranslateCardType(cardType);
  }

  // ترجمة احتياطية للأنواع المعروفة
  String _fallbackTranslateCardType(String cardType) {
    // تنظيف النص مع الاحتفاظ بالأرقام للأنواع المخصصة
    final originalLower = cardType.trim().toLowerCase();

    // إذا كان النوع مخصص بأرقام (مثل Custom_3, Custom_5)
    if (originalLower.startsWith('custom_') &&
        RegExp(r'custom_\d+$').hasMatch(originalLower)) {
      final number = originalLower.replaceAll('custom_', '');
      return 'نوع مخصص $number';
    }

    final cleanType = cardType
        .trim()
        .toLowerCase()
        .replaceAll(
          RegExp(r'[^a-zA-Z\u0600-\u06FF]'),
          '',
        ) // إزالة كل شيء عدا الحروف
        .replaceAll('custom', '') // إزالة كلمة custom
        .trim();

    // البحث الذكي بالكلمات المفتاحية - حل جذري وشامل

    // زين - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'zain',
      'زين',
      'zaincard',
      'zaincash',
      'zainmoney',
      'zainiraq',
      'zaintelecom',
      'zaintel',
      'zn',
      'zain5000',
      'zain10000',
    ])) {
      return 'زين';
    }

    // آسيا - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'asia',
      'sia',
      'آسيا',
      'اسيا',
      'asiacard',
      'siacard',
      'asiacash',
      'siacash',
      'asiacell',
      'siacell',
      'asiatelecom',
      'siatelecom',
      'asiacel',
      'siacel',
      'asia5000',
      'sia5000',
      'asia10000',
      'sia10000',
    ])) {
      return 'آسيا';
    }

    // أبو العشرة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abuashara',
      'ashara',
      'abuashara',
      'abuasharacard',
      'asharacard',
      'abuasharacash',
      'asharacash',
      'أبوالعشرة',
      'ابوالعشرة',
      'أبوعشرة',
      'ابوعشرة',
      'العشرة',
      'عشرة',
    ])) {
      return 'أبو العشرة';
    }

    // أبو الستة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abusitta',
      'sitta',
      'abusitta',
      'abusittacard',
      'sittacard',
      'abusittacash',
      'sittacash',
      'أبوالستة',
      'ابوالستة',
      'أبوستة',
      'ابوستة',
      'الستة',
      'ستة',
    ])) {
      return 'أبو الستة';
    }

    // الأنواع الأساسية
    if (_containsAny(cleanType, ['cash', 'نقدي'])) {
      return 'نقدي';
    }

    if (_containsAny(cleanType, ['visa', 'فيزا'])) {
      return 'فيزا';
    }

    if (_containsAny(cleanType, ['mastercard', 'master', 'ماستر'])) {
      return 'ماستركارد';
    }

    if (_containsAny(cleanType, [
      'americanexpress',
      'american',
      'express',
      'امريكان',
    ])) {
      return 'أمريكان إكسبريس';
    }

    if (_containsAny(cleanType, ['other', 'اخرى', 'أخرى'])) {
      return 'أخرى';
    }

    // إذا لم نجد مطابقة، نعيد الاسم الأصلي مع تحسين التنسيق
    return _capitalizeFirst(cardType.trim());
  }

  // دالة مساعدة للبحث في قائمة من الكلمات المفتاحية - محسنة
  bool _containsAny(String text, List<String> keywords) {
    final lowerText = text.toLowerCase();
    for (final keyword in keywords) {
      final lowerKeyword = keyword.toLowerCase();
      // بحث دقيق ومرن
      if (lowerText.contains(lowerKeyword) ||
          lowerText == lowerKeyword ||
          lowerText.startsWith(lowerKeyword) ||
          lowerText.endsWith(lowerKeyword)) {
        return true;
      }
    }
    return false;
  }

  // تحسين تنسيق النص
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // فحص إذا كان النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  void _showAddStockDialog(BuildContext context, [String? cardType]) {
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );
    String? selectedCardType = cardType;
    final quantityController = TextEditingController();
    final minQuantityController = TextEditingController(text: '10');
    final priceController = TextEditingController();

    // تحديث السعر الافتراضي عند تغيير نوع الكارت
    void updateDefaultPrice() async {
      if (selectedCardType != null) {
        // محاولة الحصول على السعر المحفوظ أولاً
        final savedPrice = await _getSavedCardPrice(selectedCardType!);
        if (savedPrice != null && savedPrice > 0) {
          priceController.text = _formatCurrency(savedPrice);
          debugPrint('🔄 تم تحديث السعر من المحفوظات: $savedPrice');
        } else {
          // استخدام السعر من قاعدة البيانات أو الافتراضي
          final defaultPrice = _getDefaultPrice(selectedCardType!);
          priceController.text = _formatCurrency(defaultPrice);
          debugPrint('🔄 تم تحديث السعر الافتراضي: $defaultPrice');
        }
      }
    }

    // تعيين السعر الافتراضي عند فتح النافذة
    if (selectedCardType != null) {
      updateDefaultPrice();
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_box,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  cardType != null
                      ? 'إضافة كمية لـ ${_translateCardType(cardType)}'
                      : 'إضافة كمية جديدة',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (cardType == null) ...[
                  // اختيار نوع البطاقة
                  DropdownButtonFormField<String>(
                    value: selectedCardType,
                    decoration: InputDecoration(
                      labelText: 'نوع البطاقة',
                      labelStyle: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.blue.shade600,
                          width: 2,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      prefixIcon: Icon(
                        Icons.credit_card,
                        color: Colors.blue.shade600,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                    ),
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    dropdownColor: Colors.white,
                    items: _getUniqueCardTypes(cardTypeProvider),
                    onChanged: (value) {
                      setState(() {
                        selectedCardType = value;
                      });
                      updateDefaultPrice();
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // الكمية المضافة
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    labelText: 'الكمية المضافة',
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.green.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon: Icon(Icons.add, color: Colors.green.shade600),
                    suffixText: 'بطاقة',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
                const SizedBox(height: 16),

                // السعر
                TextField(
                  controller: priceController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                    ThousandsSeparatorInputFormatter(),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  onTap: () {
                    // تحديد النص بالكامل عند التركيز لتسهيل الإدخال
                    if (priceController.text.isNotEmpty) {
                      priceController.selection = TextSelection(
                        baseOffset: 0,
                        extentOffset: priceController.text.length,
                      );
                    }
                  },
                  decoration: InputDecoration(
                    labelText: 'السعر للوحدة (بالآلاف)',
                    hintText: 'أدخل السعر',
                    hintStyle: TextStyle(color: Colors.grey.shade400),
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.blue.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon:
                        Icon(Icons.attach_money, color: Colors.blue.shade600),
                    suffixText: 'ألف',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
                const SizedBox(height: 16),

                // الحد الأدنى
                TextField(
                  controller: minQuantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    labelText: 'الحد الأدنى للتنبيه',
                    labelStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.orange.shade600,
                        width: 2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade400),
                    ),
                    prefixIcon: Icon(
                      Icons.warning,
                      color: Colors.orange.shade600,
                    ),
                    suffixText: 'بطاقة',
                    suffixStyle: TextStyle(color: Colors.grey.shade600),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade700,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedCardType != null &&
                    quantityController.text.isNotEmpty) {
                  try {
                    final quantity = int.parse(quantityController.text);
                    final price = double.tryParse(
                            priceController.text.replaceAll(',', '')) ??
                        0.0;

                    final inventoryProvider =
                        Provider.of<CardInventoryProvider>(
                      context,
                      listen: false,
                    );
                    await inventoryProvider.addStock(
                      selectedCardType!,
                      quantity,
                      price: price,
                    );

                    // حفظ السعر في SharedPreferences للمرة القادمة
                    await _saveCardPrice(selectedCardType!, price);

                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'تم إضافة $quantity بطاقة من ${_translateCardType(selectedCardType!)} بسعر ${_formatCurrency(price)} ألف',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'إضافة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<DropdownMenuItem<String>> _getUniqueCardTypes(
    CardTypeProvider cardTypeProvider,
  ) {
    final cardTypes = <String>{};

    try {
      for (final cardType in cardTypeProvider.allCardTypes) {
        if (cardType.displayName.isNotEmpty) {
          cardTypes.add(cardType.displayName);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع البطاقات: $e');
    }

    final sortedTypes = cardTypes.toList()..sort();

    return sortedTypes.map((cardType) {
      return DropdownMenuItem<String>(
        value: cardType,
        child: Text(cardType, style: const TextStyle(fontSize: 16)),
      );
    }).toList();
  }

  void _showEditInventoryDialog(BuildContext context, CardInventory inventory) {
    final quantityController = TextEditingController(
      text: inventory.quantity.toString(),
    );
    final minQuantityController = TextEditingController(
      text: inventory.minQuantity.toString(),
    );
    final priceController = TextEditingController(
      text: inventory.price.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit, color: Colors.orange.shade600, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'تعديل ${_translateCardType(inventory.cardType)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الكمية الحالية
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'الكمية الحالية',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.orange.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon: Icon(
                  Icons.inventory,
                  color: Colors.orange.shade600,
                ),
                suffixText: 'بطاقة',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
            const SizedBox(height: 16),

            // السعر
            TextField(
              controller: priceController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'السعر للوحدة (بالآلاف)',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.blue.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon:
                    Icon(Icons.attach_money, color: Colors.blue.shade600),
                suffixText: 'ألف',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              onChanged: (value) {
                // إضافة فاصل الآلاف
                if (value.isNotEmpty) {
                  final numericValue = value.replaceAll(',', '');
                  if (double.tryParse(numericValue) != null) {
                    final formatted =
                        _formatCurrency(double.parse(numericValue));
                    if (formatted != value) {
                      priceController.value = TextEditingValue(
                        text: formatted,
                        selection:
                            TextSelection.collapsed(offset: formatted.length),
                      );
                    }
                  }
                }
              },
            ),
            const SizedBox(height: 16),

            // الحد الأدنى
            TextField(
              controller: minQuantityController,
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                labelText: 'الحد الأدنى للتنبيه',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade600, width: 2),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon: Icon(Icons.warning, color: Colors.red.shade600),
                suffixText: 'بطاقة',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (quantityController.text.isNotEmpty &&
                  minQuantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty) {
                try {
                  final quantity = int.parse(quantityController.text);
                  final minQuantity = int.parse(minQuantityController.text);
                  final price =
                      double.parse(priceController.text.replaceAll(',', ''));

                  final updatedInventory = inventory.copyWith(
                    quantity: quantity,
                    minQuantity: minQuantity,
                    price: price,
                    updatedAt: DateTime.now(),
                  );

                  final inventoryProvider = Provider.of<CardInventoryProvider>(
                    context,
                    listen: false,
                  );
                  await inventoryProvider.updateInventory(updatedInventory);

                  // حفظ السعر المحدث في SharedPreferences
                  await _saveCardPrice(inventory.cardType, price);

                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم تحديث ${inventory.cardType} بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'تحديث',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    CardInventory inventory,
    CardInventoryProvider inventoryProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red.shade600),
            const SizedBox(width: 8),
            const Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'هل أنت متأكد من حذف ${inventory.cardType}؟',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Column(
                children: [
                  Text(
                    'الكمية الحالية: ${inventory.quantity} بطاقة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'سيتم حذف جميع البيانات المتعلقة بهذا النوع',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await inventoryProvider.deleteInventory(inventory.id!);

                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف ${inventory.cardType} بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // دالة تنسيق تاريخ الشراء واسم اليوم والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // أسماء الأيام بالعربية
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    // أسماء الأشهر بالترقيم العربي
    const monthNames = [
      'الأول',
      'الثاني',
      'الثالث',
      'الرابع',
      'الخامس',
      'السادس',
      'السابع',
      'الثامن',
      'التاسع',
      'العاشر',
      'الحادي عشر',
      'الثاني عشر',
    ];

    final dayName = dayNames[dateTime.weekday - 1];
    final monthName = monthNames[dateTime.month - 1];

    // تنسيق الوقت (12 ساعة)
    final hour = dateTime.hour == 0
        ? 12
        : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final period = dateTime.hour >= 12 ? 'مساءً' : 'صباحاً';
    final minute = dateTime.minute.toString().padLeft(2, '0');

    // تنسيق التاريخ بالشكل المطلوب: 2025\6\3 شهر السادس
    final dateFormat =
        '${dateTime.year}\\${dateTime.month}\\${dateTime.day} شهر $monthName';

    if (difference.inDays == 0) {
      return 'اليوم ($dayName) - $dateFormat - الساعة $hour:$minute $period';
    } else if (difference.inDays == 1) {
      return 'أمس - $dateFormat - الساعة $hour:$minute $period';
    } else {
      return '$dayName - $dateFormat - الساعة $hour:$minute $period';
    }
  }

  // دالة تنسيق العملة
  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(3).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  // دالة عرض نافذة تعديل السعر
  void _showEditPriceDialog(BuildContext context, CardInventory inventory) {
    final priceController =
        TextEditingController(text: inventory.price.toString());

    // متغير لتحديث الإجمالي
    final ValueNotifier<double> totalNotifier =
        ValueNotifier(inventory.totalValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.attach_money,
                color: Colors.blue.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'تعديل سعر ${_translateCardType(inventory.cardType)}',
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: priceController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                labelText: 'السعر (بالآلاف)',
                labelStyle: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.blue.shade600,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                prefixIcon:
                    Icon(Icons.attach_money, color: Colors.blue.shade600),
                suffixText: 'ألف',
                suffixStyle: TextStyle(color: Colors.grey.shade600),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              onChanged: (value) {
                final price = double.tryParse(value) ?? 0.0;
                totalNotifier.value = price * inventory.quantity;
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      color: Colors.green.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ValueListenableBuilder<double>(
                      valueListenable: totalNotifier,
                      builder: (context, total, child) {
                        return Text(
                          'الإجمالي سيكون: ${_formatCurrency(total)} ألف',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (priceController.text.isNotEmpty) {
                try {
                  final price =
                      double.parse(priceController.text.replaceAll(',', ''));

                  final updatedInventory = inventory.copyWith(
                    price: price,
                    updatedAt: DateTime.now(),
                  );

                  final inventoryProvider = Provider.of<CardInventoryProvider>(
                    context,
                    listen: false,
                  );
                  await inventoryProvider.updateInventory(updatedInventory);

                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content:
                            Text('تم تحديث سعر ${inventory.cardType} بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // حفظ السعر في SharedPreferences
  Future<void> _saveCardPrice(String cardType, double price) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('card_price_$cardType', price);
    debugPrint('💾 تم حفظ السعر لـ $cardType: $price');
  }

  // استرجاع السعر من SharedPreferences
  Future<double?> _getSavedCardPrice(String cardType) async {
    final prefs = await SharedPreferences.getInstance();
    final price = prefs.getDouble('card_price_$cardType');
    if (price != null) {
      debugPrint('📖 تم استرجاع السعر المحفوظ لـ $cardType: $price');
    }
    return price;
  }

  // دالة متزامنة للحصول على السعر (للاستخدام الفوري)
  double _getDefaultPrice(String cardType) {
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );

    // البحث في قاعدة البيانات أولاً
    if (!inventoryProvider.isLoading &&
        inventoryProvider.inventories.isNotEmpty) {
      final realPrice = inventoryProvider.getCardPrice(cardType);
      if (realPrice > 0) {
        return realPrice;
      }
    }

    // الأسعار الافتراضية
    final translatedType = _translateCardType(cardType);
    switch (translatedType) {
      case 'زين':
        return 6.750;
      case 'آسيا':
        return 7.750;
      case 'أبو العشرة':
        return 9.800;
      case 'أبو الستة':
        return 4.800;
      case 'نقدي':
        return 0.0;
      default:
        return 5.000;
    }
  }
}

// TextInputFormatter لفاصل الآلاف
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // إذا كان النص فارغ، إرجاع القيمة كما هي
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // إزالة جميع الفواصل الموجودة
    final String cleanText = newValue.text.replaceAll(',', '');

    // التحقق من صحة الرقم
    if (double.tryParse(cleanText) == null) {
      return oldValue;
    }

    // إضافة فواصل الآلاف
    final String formattedText = _addThousandsSeparator(cleanText);

    // حساب موضع المؤشر الجديد
    int cursorPosition = newValue.selection.baseOffset;

    // إذا كان المؤشر في نهاية النص، ضعه في نهاية النص المنسق
    if (cursorPosition >= newValue.text.length) {
      cursorPosition = formattedText.length;
    } else {
      // حساب عدد الفواصل المضافة قبل موضع المؤشر
      final String textBeforeCursor =
          newValue.text.substring(0, cursorPosition);
      final String cleanTextBeforeCursor = textBeforeCursor.replaceAll(',', '');
      final String formattedTextBeforeCursor =
          _addThousandsSeparator(cleanTextBeforeCursor);
      cursorPosition = formattedTextBeforeCursor.length;
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(
          offset: cursorPosition.clamp(0, formattedText.length)),
    );
  }

  String _addThousandsSeparator(String value) {
    if (value.isEmpty) return value;

    // فصل الجزء الصحيح عن الجزء العشري
    final List<String> parts = value.split('.');
    final String integerPart = parts[0];
    final String decimalPart = parts.length > 1 ? parts[1] : '';

    // إضافة فواصل الآلاف للجزء الصحيح
    final String formattedInteger = integerPart.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );

    // إعادة تجميع الرقم
    if (decimalPart.isNotEmpty) {
      return '$formattedInteger.$decimalPart';
    } else {
      return formattedInteger;
    }
  }

  // بطاقة الإحصائيات والتحليل القابلة للطي
  Widget _buildStatisticsCard(CardInventoryProvider inventoryProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.analytics,
            color: Colors.blue.shade700,
            size: 24,
          ),
        ),
        title: const Text(
          'الإحصائيات والتحليل',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          'عرض تفصيلي لإحصائيات المخزون',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildStatisticsContent(inventoryProvider),
          ),
        ],
      ),
    );
  }
}


    return Container(
      padding = const EdgeInsets.all(16),
      decoration = BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade700],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.inventory,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إحصائيات المخزون الشاملة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الصف الأول: إجمالي الأنواع وإجمالي الكمية
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'إجمالي الأنواع',
                  totalTypes.toString(),
                  _build3DIcon(
                      Icons.category, Colors.purple, Colors.purpleAccent),
                  Colors.grey,
                  totalTypes,
                  'نوع',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'إجمالي الكمية',
                  totalQuantity.toString(),
                  _build3DIcon(
                      Icons.inventory_2, Colors.green, Colors.lightGreen),
                  Colors.grey,
                  totalQuantity,
                  'بطاقة',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: إجمالي القيمة ومتوسط الكمية
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'إجمالي القيمة',
                  _formatCurrency(totalValue),
                  _build3DIcon(
                      Icons.attach_money, Colors.orange, Colors.orangeAccent),
                  Colors.grey,
                  totalValue.toInt(),
                  'ألف',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'متوسط الكمية',
                  averageQuantityPerType.toString(),
                  _build3DIcon(Icons.analytics, Colors.teal, Colors.tealAccent),
                  Colors.grey,
                  averageQuantityPerType,
                  'بطاقة/نوع',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // إحصائيات القيمة والاستثمار
  Widget _buildInventoryValueStats(CardInventoryProvider inventoryProvider) {
    final inventories = inventoryProvider.inventories;
    final totalValue = inventoryProvider.getTotalInventoryValue();
    final averageValuePerType =
        inventories.isNotEmpty ? (totalValue / inventories.length) : 0.0;

    // حساب أغلى وأرخص نوع
    double maxValue = 0.0;
    double minValue = double.infinity;
    String maxValueType = 'غير محدد';
    String minValueType = 'غير محدد';

    for (final inventory in inventories) {
      final value = inventory.totalValue;
      if (value > maxValue) {
        maxValue = value;
        maxValueType = inventory.cardType;
      }
      if (value < minValue && value > 0) {
        minValue = value;
        minValueType = inventory.cardType;
      }
    }

    if (minValue == double.infinity) minValue = 0.0;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade600, Colors.green.shade700],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.monetization_on,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إحصائيات القيمة والاستثمار',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الصف الأول: إجمالي الاستثمار ومتوسط القيمة
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'إجمالي الاستثمار',
                  _formatCurrency(totalValue),
                  _build3DIcon(Icons.account_balance_wallet, Colors.green,
                      Colors.lightGreen),
                  Colors.grey,
                  totalValue.toInt(),
                  'ألف',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'متوسط القيمة',
                  _formatCurrency(averageValuePerType),
                  _build3DIcon(
                      Icons.trending_up, Colors.blue, Colors.lightBlue),
                  Colors.grey,
                  averageValuePerType.toInt(),
                  'ألف/نوع',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: أعلى وأقل قيمة
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'أعلى قيمة',
                  _formatCurrency(maxValue),
                  _build3DIcon(Icons.star, Colors.amber, Colors.amberAccent),
                  Colors.grey,
                  maxValue.toInt(),
                  maxValueType.length > 8
                      ? '${maxValueType.substring(0, 8)}...'
                      : maxValueType,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'أقل قيمة',
                  _formatCurrency(minValue),
                  _build3DIcon(
                      Icons.trending_down, Colors.orange, Colors.orangeAccent),
                  Colors.grey,
                  minValue.toInt(),
                  minValueType.length > 8
                      ? '${minValueType.substring(0, 8)}...'
                      : minValueType,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // إحصائيات الحالة والتنبيهات
  Widget _buildInventoryStatusStats(CardInventoryProvider inventoryProvider) {
    final inventories = inventoryProvider.inventories;
    final lowStockItems = inventoryProvider.getLowStockItems();
    final outOfStockItems = inventoryProvider.getOutOfStockItems();
    final availableItems =
        inventories.where((inv) => !inv.isLowStock && !inv.isOutOfStock).length;
    final totalItems = inventories.length;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade600, Colors.orange.shade700],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.warning_amber,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إحصائيات الحالة والتنبيهات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الصف الأول: متوفر ومنخفض
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'متوفر',
                  availableItems.toString(),
                  _build3DIcon(
                      Icons.check_circle, Colors.green, Colors.lightGreen),
                  Colors.grey,
                  availableItems,
                  'نوع',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'منخفض',
                  lowStockItems.length.toString(),
                  _build3DIcon(
                      Icons.warning, Colors.orange, Colors.orangeAccent),
                  Colors.grey,
                  lowStockItems.length,
                  'نوع',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: نافد ونسبة التوفر
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'نافد',
                  outOfStockItems.length.toString(),
                  _build3DIcon(Icons.error, Colors.red, Colors.redAccent),
                  Colors.grey,
                  outOfStockItems.length,
                  'نوع',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'نسبة التوفر',
                  totalItems > 0
                      ? '${((availableItems / totalItems) * 100).toStringAsFixed(1)}%'
                      : '0%',
                  _build3DIcon(
                      Icons.pie_chart, Colors.purple, Colors.purpleAccent),
                  Colors.grey,
                  totalItems > 0
                      ? ((availableItems / totalItems) * 100).round()
                      : 0,
                  '%',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // إحصائيات زمنية للمخزون
  Widget _buildInventoryTimeStats(CardInventoryProvider inventoryProvider) {
    final inventories = inventoryProvider.inventories;

    // حساب الإحصائيات الزمنية
    int todayAdded = 0;
    int thisWeekAdded = 0;
    int thisMonthAdded = 0;
    int oldestDays = 0;
    String oldestType = 'غير محدد';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: now.weekday - 1));
    final monthStart = DateTime(now.year, now.month);

    DateTime? oldestDate;

    for (final inventory in inventories) {
      final createdDate = DateTime(
        inventory.createdAt.year,
        inventory.createdAt.month,
        inventory.createdAt.day,
      );

      // اليوم
      if (createdDate.isAtSameMomentAs(today)) {
        todayAdded++;
      }

      // هذا الأسبوع
      if (createdDate.isAfter(weekStart.subtract(const Duration(days: 1)))) {
        thisWeekAdded++;
      }

      // هذا الشهر
      if (createdDate.isAfter(monthStart.subtract(const Duration(days: 1)))) {
        thisMonthAdded++;
      }

      // الأقدم
      if (oldestDate == null || inventory.createdAt.isBefore(oldestDate)) {
        oldestDate = inventory.createdAt;
        oldestType = inventory.cardType;
      }
    }

    if (oldestDate != null) {
      oldestDays = now.difference(oldestDate).inDays;
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade600, Colors.purple.shade700],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.access_time,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'إحصائيات زمنية للمخزون',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الصف الأول: اليوم وهذا الأسبوع
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'مضاف اليوم',
                  todayAdded.toString(),
                  _build3DIcon(Icons.today, Colors.blue, Colors.lightBlue),
                  Colors.grey,
                  todayAdded,
                  'نوع',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'هذا الأسبوع',
                  thisWeekAdded.toString(),
                  _build3DIcon(
                      Icons.date_range, Colors.green, Colors.lightGreen),
                  Colors.grey,
                  thisWeekAdded,
                  'نوع',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: هذا الشهر والأقدم
          Row(
            children: [
              Expanded(
                child: _buildEnhancedStatCard(
                  'هذا الشهر',
                  thisMonthAdded.toString(),
                  _build3DIcon(
                      Icons.calendar_month, Colors.orange, Colors.orangeAccent),
                  Colors.grey,
                  thisMonthAdded,
                  'نوع',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEnhancedStatCard(
                  'الأقدم',
                  oldestDays > 0 ? '$oldestDays يوم' : 'اليوم',
                  _build3DIcon(
                      Icons.history, Colors.grey, Colors.grey.shade400),
                  Colors.grey,
                  oldestDays,
                  oldestType.length > 8
                      ? '${oldestType.substring(0, 8)}...'
                      : oldestType,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء أيقونة ثلاثية الأبعاد
  Widget _build3DIcon(IconData icon, Color primaryColor, Color accentColor) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [primaryColor, accentColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  // بناء بطاقة إحصائية محسنة
  Widget _buildEnhancedStatCard(
    String title,
    String value,
    Widget icon,
    Color borderColor,
    int numericValue,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان والأيقونة في الأعلى
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // القيمة في الوسط
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
            textAlign: TextAlign.center,
          ),
          // الوحدة تحت القيمة (إذا كانت موجودة)
          if (unit.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              unit,
              style: TextStyle(
                fontSize: 9,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
