import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/card_stock.dart';
import '../models/card_profit.dart';
import '../models/card_inventory.dart';
import '../models/custom_card_type.dart';

class BackupService {
  factory BackupService() => _instance;
  BackupService._internal();
  static final BackupService _instance = BackupService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إنشاء نسخة احتياطية كاملة
  Future<String> createFullBackup() async {
    try {
      debugPrint('🔄 بدء إنشاء النسخة الاحتياطية...');

      // جلب جميع البيانات من قاعدة البيانات
      final customers = await _databaseHelper.getAllCustomers();
      final debts = await _databaseHelper.getAllDebts();
      // جلب جميع المدفوعات من قاعدة البيانات
      final db = await _databaseHelper.database;
      final paymentMaps = await db.query(
        'payments',
        orderBy: 'payment_date DESC',
      );
      final payments = paymentMaps.map((map) => Payment.fromMap(map)).toList();
      final cardStocks = await _databaseHelper.getAllCardStocks();
      final cardProfits = await _databaseHelper.getAllCardProfits();
      final cardInventories = await _databaseHelper.getAllCardInventories();
      final customCardTypes = await _databaseHelper.getAllCustomCardTypes();

      // إنشاء كائن النسخة الاحتياطية
      final backupData = {
        'backup_info': {
          'version': '1.0',
          'created_at': DateTime.now().toIso8601String(),
          'app_name': 'محاسب ديون احترافي',
          'total_customers': customers.length,
          'total_debts': debts.length,
          'total_payments': payments.length,
          'total_card_stocks': cardStocks.length,
          'total_card_profits': cardProfits.length,
          'total_card_inventories': cardInventories.length,
          'total_custom_card_types': customCardTypes.length,
        },
        'customers': customers.map((customer) => customer.toMap()).toList(),
        'debts': debts.map((debt) => debt.toMap()).toList(),
        'payments': payments.map((payment) => payment.toMap()).toList(),
        'card_stocks': cardStocks.map((stock) => stock.toMap()).toList(),
        'card_profits': cardProfits.map((profit) => profit.toMap()).toList(),
        'card_inventories': cardInventories
            .map((inventory) => inventory.toMap())
            .toList(),
        'custom_card_types': customCardTypes
            .map((type) => type.toMap())
            .toList(),
      };

      // تحويل إلى JSON
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      // حفظ الملف
      final filePath = await _saveBackupFile(jsonString);

      debugPrint('✅ تم إنشاء النسخة الاحتياطية بنجاح: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      throw Exception('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }

  // حفظ ملف النسخة الاحتياطية
  Future<String> _saveBackupFile(String jsonContent) async {
    try {
      Directory? backupDir;

      if (Platform.isAndroid) {
        // محاولة الحفظ في Downloads أولاً
        try {
          await _requestStoragePermissions();
          backupDir = Directory('/storage/emulated/0/Download/DebtAccountant');

          // اختبار إمكانية الكتابة
          if (!await backupDir.exists()) {
            await backupDir.create(recursive: true);
          }

          // اختبار الكتابة
          final testFile = File('${backupDir.path}/test.txt');
          await testFile.writeAsString('test');
          await testFile.delete();

          debugPrint('✅ سيتم الحفظ في مجلد Downloads');
        } catch (e) {
          debugPrint(
            '⚠️ لا يمكن الحفظ في Downloads، سيتم الحفظ في التخزين الداخلي',
          );
          // استخدام التخزين الداخلي كبديل
          final appDir = await getApplicationDocumentsDirectory();
          backupDir = Directory('${appDir.path}/backups');
        }
      } else {
        // للـ iOS - استخدام مجلد Documents
        final appDir = await getApplicationDocumentsDirectory();
        backupDir = Directory('${appDir.path}/backups');
      }

      // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateFormat(
        'yyyy-MM-dd_HH-mm-ss',
      ).format(DateTime.now());
      final fileName = 'محاسب_ديون_نسخة_احتياطية_$timestamp.json';
      final filePath = '${backupDir.path}/$fileName';

      // كتابة الملف
      final file = File(filePath);
      await file.writeAsString(jsonContent);

      debugPrint('✅ تم حفظ النسخة الاحتياطية في: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في حفظ ملف النسخة الاحتياطية: $e');
      throw Exception('فشل في حفظ ملف النسخة الاحتياطية: $e');
    }
  }

  // طلب صلاحيات التخزين
  Future<void> _requestStoragePermissions() async {
    if (Platform.isAndroid) {
      // للأندرويد 13+ (API 33+)
      if (await Permission.photos.request().isGranted ||
          await Permission.videos.request().isGranted ||
          await Permission.audio.request().isGranted) {
        return; // الصلاحيات ممنوحة
      }

      // للأندرويد 11-12 (API 30-32)
      if (await Permission.storage.request().isGranted) {
        return; // الصلاحيات ممنوحة
      }

      // للأندرويد 11+ محاولة طلب صلاحية إدارة الملفات
      if (await Permission.manageExternalStorage.request().isGranted) {
        return; // الصلاحيات ممنوحة
      }

      // إذا لم تُمنح أي صلاحيات، استخدم التخزين الداخلي
      debugPrint(
        '⚠️ لم يتم منح صلاحيات التخزين، سيتم الحفظ في التخزين الداخلي',
      );
    }
  }

  // اختيار ملف نسخة احتياطية من ذاكرة الهاتف
  Future<String?> pickBackupFile() async {
    try {
      await _requestStoragePermissions();

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'اختر ملف النسخة الاحتياطية',
      );

      if (result != null && result.files.single.path != null) {
        return result.files.single.path!;
      }
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في اختيار الملف: $e');
      throw Exception('فشل في اختيار ملف النسخة الاحتياطية: $e');
    }
  }

  // مشاركة ملف النسخة الاحتياطية
  Future<void> shareBackupFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      await share_plus.Share.shareXFiles(
        [share_plus.XFile(filePath)],
        text: 'نسخة احتياطية من تطبيق محاسب ديون احترافي',
        subject: 'نسخة احتياطية - محاسب ديون احترافي',
      );

      debugPrint('✅ تم مشاركة النسخة الاحتياطية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة النسخة الاحتياطية: $e');
      throw Exception('فشل في مشاركة النسخة الاحتياطية: $e');
    }
  }

  // الحصول على قائمة النسخ الاحتياطية المحفوظة
  Future<List<FileSystemEntity>> getBackupFiles() async {
    try {
      Directory? backupDir;

      if (Platform.isAndroid) {
        backupDir = Directory('/storage/emulated/0/Download/DebtAccountant');
      } else {
        final appDir = await getApplicationDocumentsDirectory();
        backupDir = Directory('${appDir.path}/backups');
      }

      if (!await backupDir.exists()) {
        return [];
      }

      final files = backupDir
          .listSync()
          .where((file) => file.path.endsWith('.json'))
          .toList();

      // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
      files.sort(
        (a, b) => b.statSync().modified.compareTo(a.statSync().modified),
      );

      return files;
    } catch (e) {
      debugPrint('❌ خطأ في جلب قائمة النسخ الاحتياطية: $e');
      return [];
    }
  }

  // استرجاع النسخة الاحتياطية
  Future<bool> restoreFromBackup(String filePath) async {
    try {
      debugPrint('🔄 بدء استرجاع النسخة الاحتياطية من: $filePath');

      // قراءة الملف
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final jsonContent = await file.readAsString();
      final backupData = jsonDecode(jsonContent) as Map<String, dynamic>;

      // التحقق من صحة البيانات
      if (!_validateBackupData(backupData)) {
        throw Exception('ملف النسخة الاحتياطية تالف أو غير صحيح');
      }

      // مسح البيانات الحالية (اختياري - يمكن إضافة خيار للمستخدم)
      await _clearAllData();

      // استرجاع البيانات
      await _restoreCustomers(backupData['customers'] as List);
      await _restoreDebts(backupData['debts'] as List);
      await _restorePayments(backupData['payments'] as List);
      await _restoreCardStocks(backupData['card_stocks'] as List);
      await _restoreCardProfits(backupData['card_profits'] as List);
      await _restoreCardInventories(backupData['card_inventories'] as List);
      await _restoreCustomCardTypes(backupData['custom_card_types'] as List);

      debugPrint('✅ تم استرجاع النسخة الاحتياطية بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع النسخة الاحتياطية: $e');
      throw Exception('فشل في استرجاع النسخة الاحتياطية: $e');
    }
  }

  // التحقق من صحة بيانات النسخة الاحتياطية
  bool _validateBackupData(Map<String, dynamic> data) {
    try {
      // التحقق من وجود المفاتيح الأساسية
      final requiredKeys = [
        'backup_info',
        'customers',
        'debts',
        'payments',
        'card_stocks',
        'card_profits',
        'card_inventories',
        'custom_card_types',
      ];

      for (final key in requiredKeys) {
        if (!data.containsKey(key)) {
          debugPrint('❌ مفتاح مفقود في النسخة الاحتياطية: $key');
          return false;
        }
      }

      // التحقق من معلومات النسخة الاحتياطية
      final backupInfo = data['backup_info'] as Map<String, dynamic>;
      if (!backupInfo.containsKey('version') ||
          !backupInfo.containsKey('created_at')) {
        debugPrint('❌ معلومات النسخة الاحتياطية غير مكتملة');
        return false;
      }

      debugPrint('✅ النسخة الاحتياطية صحيحة');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من النسخة الاحتياطية: $e');
      return false;
    }
  }

  // مسح جميع البيانات الحالية
  Future<void> _clearAllData() async {
    try {
      debugPrint('🗑️ مسح البيانات الحالية...');

      final db = await _databaseHelper.database;

      // مسح البيانات بالترتيب الصحيح (بسبب Foreign Keys)
      await db.delete('payments');
      await db.delete('debts');
      await db.delete('customers');
      await db.delete('card_stocks');
      await db.delete('card_profits');
      await db.delete('card_inventories');
      await db.delete('custom_card_types');

      debugPrint('✅ تم مسح البيانات الحالية');
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات: $e');
      throw Exception('فشل في مسح البيانات الحالية: $e');
    }
  }

  // استرجاع العملاء
  Future<void> _restoreCustomers(List customersList) async {
    try {
      debugPrint('🔄 استرجاع العملاء...');

      for (final customerData in customersList) {
        final customer = Customer.fromMap(customerData as Map<String, dynamic>);
        await _databaseHelper.insertCustomer(customer);
      }

      debugPrint('✅ تم استرجاع ${customersList.length} عميل');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع العملاء: $e');
      throw Exception('فشل في استرجاع العملاء: $e');
    }
  }

  // استرجاع الديون
  Future<void> _restoreDebts(List debtsList) async {
    try {
      debugPrint('🔄 استرجاع الديون...');

      for (final debtData in debtsList) {
        final debt = Debt.fromMap(debtData as Map<String, dynamic>);
        await _databaseHelper.insertDebt(debt);
      }

      debugPrint('✅ تم استرجاع ${debtsList.length} دين');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع الديون: $e');
      throw Exception('فشل في استرجاع الديون: $e');
    }
  }

  // استرجاع المدفوعات
  Future<void> _restorePayments(List paymentsList) async {
    try {
      debugPrint('🔄 استرجاع المدفوعات...');

      for (final paymentData in paymentsList) {
        final payment = Payment.fromMap(paymentData as Map<String, dynamic>);
        await _databaseHelper.insertPayment(payment);
      }

      debugPrint('✅ تم استرجاع ${paymentsList.length} دفعة');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع المدفوعات: $e');
      throw Exception('فشل في استرجاع المدفوعات: $e');
    }
  }

  // استرجاع مخزون الكروت
  Future<void> _restoreCardStocks(List stocksList) async {
    try {
      debugPrint('🔄 استرجاع مخزون الكروت...');

      for (final stockData in stocksList) {
        final stock = CardStock.fromMap(stockData as Map<String, dynamic>);
        await _databaseHelper.insertCardStock(stock);
      }

      debugPrint('✅ تم استرجاع ${stocksList.length} مخزون كرت');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع مخزون الكروت: $e');
      throw Exception('فشل في استرجاع مخزون الكروت: $e');
    }
  }

  // استرجاع أرباح الكروت
  Future<void> _restoreCardProfits(List profitsList) async {
    try {
      debugPrint('🔄 استرجاع أرباح الكروت...');

      for (final profitData in profitsList) {
        final profit = CardProfit.fromMap(profitData as Map<String, dynamic>);
        await _databaseHelper.insertCardProfit(profit);
      }

      debugPrint('✅ تم استرجاع ${profitsList.length} ربح كرت');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع أرباح الكروت: $e');
      throw Exception('فشل في استرجاع أرباح الكروت: $e');
    }
  }

  // استرجاع جرد الكروت
  Future<void> _restoreCardInventories(List inventoriesList) async {
    try {
      debugPrint('🔄 استرجاع جرد الكروت...');

      for (final inventoryData in inventoriesList) {
        final inventory = CardInventory.fromMap(
          inventoryData as Map<String, dynamic>,
        );
        await _databaseHelper.insertCardInventory(inventory);
      }

      debugPrint('✅ تم استرجاع ${inventoriesList.length} جرد كرت');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع جرد الكروت: $e');
      throw Exception('فشل في استرجاع جرد الكروت: $e');
    }
  }

  // استرجاع أنواع الكروت المخصصة
  Future<void> _restoreCustomCardTypes(List typesList) async {
    try {
      debugPrint('🔄 استرجاع أنواع الكروت المخصصة...');

      for (final typeData in typesList) {
        final cardType = CustomCardType.fromMap(
          typeData as Map<String, dynamic>,
        );
        await _databaseHelper.insertCustomCardType(cardType);
      }

      debugPrint('✅ تم استرجاع ${typesList.length} نوع كرت مخصص');
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع أنواع الكروت المخصصة: $e');
      throw Exception('فشل في استرجاع أنواع الكروت المخصصة: $e');
    }
  }
}
