@echo off
chcp 65001 >nul
echo ========================================
echo    👁️ مراقب الملفات وبناء APK التلقائي
echo    File Watcher & Auto APK Builder
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من وجود Flutter
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo ✅ Flutter متوفر
echo.

echo 🔧 إعداد مراقب الملفات...
echo.
echo 📁 المجلدات المراقبة:
echo    - lib/
echo    - assets/
echo    - pubspec.yaml
echo    - android/app/
echo.
echo 🚀 بدء المراقبة...
echo.
echo 💡 نصائح:
echo    - سيتم بناء APK تلقائياً عند حفظ أي ملف
echo    - يمكنك إيقاف المراقبة بالضغط على Ctrl+C
echo    - APK سيتم حفظه في المجلد الرئيسي
echo.
echo ========================================
echo.

REM تشغيل مراقب الملفات Python
python scripts\file_watcher_apk.py

echo.
echo 🛑 تم إيقاف مراقب الملفات
pause
