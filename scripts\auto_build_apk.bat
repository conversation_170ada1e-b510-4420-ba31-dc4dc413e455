@echo off
echo ========================================
echo    Auto APK Builder
echo    Professional Debt Accountant
echo ========================================
echo.

REM Check Flutter installation
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Flutter not found in PATH
    pause
    exit /b 1
)

echo Flutter found
echo.

REM Get current date and time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

echo Current time: %DD%/%MM%/%YYYY% - %HH%:%Min%:%Sec%
echo.

REM Clean previous build
echo Cleaning previous build...
if exist "build" rmdir /s /q "build" 2>nul
if exist ".dart_tool\build" rmdir /s /q ".dart_tool\build" 2>nul

echo.
echo Updating dependencies...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo Failed to update dependencies
    pause
    exit /b 1
)

echo.
echo Building APK...
echo Please wait, this may take a few minutes...

REM Build APK with optimizations
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info
if %ERRORLEVEL% neq 0 (
    echo APK build failed
    pause
    exit /b 1
)

echo.
echo Copying APK to main directory...

REM Copy APK with updated name
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    copy "build\app\outputs\flutter-apk\app-release.apk" "debt_accountant_pro.apk" >nul
    echo APK copied successfully: debt_accountant_pro.apk

    REM Create builds directory if it doesn't exist
    if not exist "builds" mkdir "builds"

    REM Create timestamped copy
    copy "build\app\outputs\flutter-apk\app-release.apk" "builds\debt_accountant_%timestamp%.apk" >nul 2>nul
    if %ERRORLEVEL% equ 0 (
        echo Timestamped copy created: builds\debt_accountant_%timestamp%.apk
    )
) else (
    echo Built APK file not found
    pause
    exit /b 1
)

echo.
echo APK Information:
if exist "debt_accountant_pro.apk" (
    for %%A in ("debt_accountant_pro.apk") do (
        echo    Size: %%~zA bytes
        echo    Modified: %%~tA
    )
)

echo.
echo APK built successfully!
echo File ready: debt_accountant_pro.apk
echo.
echo You can now:
echo    - Install APK on device
echo    - Share the file
echo    - Upload to app store
echo.
pause
