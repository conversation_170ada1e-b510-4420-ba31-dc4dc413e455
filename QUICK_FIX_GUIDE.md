# دليل الإصلاح السريع - مشاكل البناء

## 🚨 التطبيق لا يتشغل - حلول فورية

### 🔥 الحل السريع (5 دقائق):

#### 1. تشغيل الإصلاح الطارئ:
```bash
emergency_fix.bat
```

#### 2. إذا فشل، جرب التطبيق البديل:
```bash
flutter run backup_main.dart
```

#### 3. إذا نجح البديل، المشكلة في الكود الأصلي

---

## 🛠️ حلول متدرجة:

### المستوى 1: تنظيف أساسي
```bash
flutter clean
flutter pub get
flutter run
```

### المستوى 2: تنظيف شامل
```bash
# حذف ملفات البناء
rmdir /s /q build
rmdir /s /q .dart_tool
del .flutter-plugins*
del pubspec.lock

# إعادة البناء
flutter pub get
flutter run
```

### المستوى 3: إصلاح التبعيات
```bash
flutter pub cache repair
flutter clean
flutter pub get
flutter run
```

### المستوى 4: استخدام pubspec مبسط
```bash
# نسخ احتياطي
copy pubspec.yaml pubspec_original.yaml

# استخدام النسخة المبسطة
copy pubspec_backup.yaml pubspec.yaml
flutter pub get
flutter run backup_main.dart
```

---

## 🔍 تشخيص المشكلة:

### تشغيل التشخيص المتقدم:
```bash
advanced_diagnosis.bat
```

### فحص يدوي:
```bash
# 1. فحص Flutter
flutter doctor -v

# 2. فحص الأجهزة
flutter devices

# 3. فحص الأخطاء
flutter analyze

# 4. اختبار البناء
flutter build apk --debug
```

---

## 🎯 تحديد نوع المشكلة:

### إذا فشل `backup_main.dart`:
**المشكلة: إعداد Flutter**
- أعد تثبيت Flutter
- تحقق من PATH
- تحقق من Android SDK

### إذا نجح `backup_main.dart` وفشل الأصلي:
**المشكلة: كود التطبيق**
- راجع أخطاء `flutter analyze`
- تحقق من التبعيات
- تحقق من imports

### إذا ظهرت أخطاء compilation:
**المشكلة: أخطاء كود**
- راجع رسائل الخطأ
- أصلح أخطاء syntax
- تحقق من أسماء الملفات

---

## 🚀 حلول الطوارئ:

### الحل 1: استخدام النسخة البديلة
```bash
# انسخ backup_main.dart إلى lib/main.dart
copy backup_main.dart lib\main.dart
flutter run
```

### الحل 2: إنشاء مشروع جديد
```bash
flutter create new_project
cd new_project
# انسخ ملفاتك تدريجياً
```

### الحل 3: استخدام Web بدلاً من Mobile
```bash
flutter run -d chrome
```

---

## 📱 مشاكل الأجهزة:

### Android:
- فعل USB Debugging
- ثبت Android SDK
- تأكد من تشغيل ADB

### Windows:
- فعل Developer Mode
- ثبت Visual Studio Build Tools

### محاكي:
- تأكد من تشغيل Android Studio
- أنشئ AVD جديد

---

## 🔧 أوامر مفيدة:

### تنظيف شامل:
```bash
flutter clean
flutter pub cache repair
flutter pub get
```

### إعادة تعيين كاملة:
```bash
rmdir /s /q build .dart_tool
del .flutter-plugins* pubspec.lock
flutter pub get
```

### اختبار سريع:
```bash
flutter run backup_main.dart
```

---

## 📞 طلب المساعدة:

### معلومات مطلوبة:
1. نتائج `flutter doctor -v`
2. رسائل الخطأ الكاملة
3. نتائج `advanced_diagnosis.bat`
4. نوع الجهاز/المحاكي

### خطوات مجربة:
- [ ] تشغيل `emergency_fix.bat`
- [ ] تشغيل `backup_main.dart`
- [ ] تشغيل `advanced_diagnosis.bat`
- [ ] مراجعة `flutter doctor`
- [ ] تجربة جهاز مختلف

---

## ✅ نصائح للوقاية:

1. **احفظ نسخة احتياطية** قبل التغييرات الكبيرة
2. **اختبر بانتظام** أثناء التطوير
3. **استخدم Git** لتتبع التغييرات
4. **نظف المشروع** بعد تحديث التبعيات
5. **احتفظ بنسخة بديلة** بسيطة تعمل دائماً
