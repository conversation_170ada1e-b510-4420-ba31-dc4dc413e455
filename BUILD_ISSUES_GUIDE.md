# دليل حل مشاكل البناء - محاسب الديون الاحترافي

## 🚨 المشكلة: التطبيق لا يتم بناؤه

### 🔍 خطوات التشخيص السريع:

#### 1. تشغيل التشخيص التلقائي:
```bash
diagnose.bat
```

#### 2. اختبار Flutter الأساسي:
```bash
flutter doctor
flutter --version
```

#### 3. اختبار بناء بسيط:
```bash
flutter run minimal_test.dart
```

---

## 🛠️ الحلول المرحلية:

### المرحلة 1: تنظيف شامل
```bash
# حذف ملفات البناء
flutter clean

# حذف ملفات إضافية
rmdir /s /q build
rmdir /s /q .dart_tool
del .flutter-plugins
del .flutter-plugins-dependencies
del pubspec.lock

# إعادة تحديث التبعيات
flutter pub get
```

### المرحلة 2: فحص الأخطاء
```bash
# فحص أخطاء الكود
flutter analyze

# فحص التبعيات
flutter pub deps
```

### المرحلة 3: اختبار متدرج
```bash
# 1. اختبار بسيط جداً
flutter run minimal_test.dart

# 2. اختبار أساسي
flutter run simple_test.dart

# 3. اختبار قاعدة البيانات
flutter run test_database.dart

# 4. التطبيق الكامل
flutter run
```

---

## 🔧 حلول المشاكل الشائعة:

### مشكلة 1: "Flutter command not found"
**الحل:**
- تأكد من تثبيت Flutter
- أضف Flutter إلى PATH
- أعد تشغيل Command Prompt

### مشكلة 2: "Pub get failed"
**الحل:**
```bash
flutter pub cache repair
flutter clean
flutter pub get
```

### مشكلة 3: "Build failed"
**الحل:**
```bash
# فحص الأخطاء
flutter analyze

# بناء تدريجي
flutter build apk --debug
```

### مشكلة 4: "Dependencies conflict"
**الحل:**
```bash
# تحديث التبعيات
flutter pub upgrade --major-versions
flutter pub get
```

---

## 📱 اختبار الأجهزة:

### فحص الأجهزة المتاحة:
```bash
flutter devices
```

### إذا لم تظهر أجهزة:
1. **Android:**
   - فعل USB Debugging
   - ثبت Android SDK
   - تأكد من تشغيل Android Studio

2. **Windows:**
   - فعل Developer Mode
   - ثبت Visual Studio

3. **Web:**
   - ثبت Chrome
   - فعل Web support

---

## 🔍 تشخيص متقدم:

### فحص سجلات مفصلة:
```bash
flutter run --verbose
flutter build apk --debug --verbose
```

### فحص ذاكرة النظام:
```bash
# Windows
tasklist /fi "imagename eq flutter*"
tasklist /fi "imagename eq dart*"
```

### فحص مساحة القرص:
- تأكد من وجود مساحة كافية (5GB+)
- احذف ملفات البناء القديمة

---

## 🚀 حلول الطوارئ:

### الحل 1: إعادة تثبيت Flutter
```bash
# حذف Flutter
rmdir /s /q C:\flutter

# إعادة تحميل وتثبيت Flutter
# من https://flutter.dev
```

### الحل 2: إنشاء مشروع جديد
```bash
# إنشاء مشروع جديد
flutter create new_mahasb

# نسخ الملفات
xcopy lib new_mahasb\lib /E /I
xcopy assets new_mahasb\assets /E /I
copy pubspec.yaml new_mahasb\
```

### الحل 3: استخدام IDE مختلف
- جرب Android Studio بدلاً من VS Code
- أو العكس

---

## 📋 قائمة فحص سريعة:

- [ ] Flutter مثبت ويعمل
- [ ] PATH محدث
- [ ] Android SDK مثبت
- [ ] USB Debugging مفعل
- [ ] مساحة قرص كافية
- [ ] اتصال إنترنت مستقر
- [ ] لا توجد برامج antivirus تحجب Flutter
- [ ] ملف pubspec.yaml صحيح
- [ ] لا توجد أخطاء في flutter analyze

---

## 📞 طلب المساعدة:

إذا استمرت المشكلة، يرجى تقديم:

1. **نتائج الأوامر:**
   ```bash
   flutter doctor -v
   flutter --version
   flutter analyze
   ```

2. **رسائل الخطأ الكاملة**

3. **معلومات النظام:**
   - نوع النظام (Windows/Mac/Linux)
   - إصدار النظام
   - مساحة القرص المتاحة

4. **خطوات إعادة إنتاج المشكلة**

---

## 🎯 نصائح للوقاية:

1. **احفظ نسخة احتياطية** من المشروع
2. **استخدم Git** لتتبع التغييرات
3. **اختبر بانتظام** أثناء التطوير
4. **حدث Flutter** بانتظام
5. **نظف المشروع** بعد التغييرات الكبيرة
