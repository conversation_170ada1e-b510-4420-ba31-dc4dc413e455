// ملف اختبار للتأكد من أن الإحصائيات تعمل بشكل صحيح
import 'lib/models/customer_statistics.dart';

void main() {
  print('اختبار نظام الإحصائيات...');

  // اختبار إنشاء إحصائيات وهمية
  final testStatistics = CustomerStatistics(
    customerId: '1',
    customerName: 'عميل تجريبي',
    totalDebtsAmount: 50000.0,
    totalDebtsCount: 5,
    totalCardsCount: 25,
    todaySalesAmount: 5000.0,
    todaySalesCount: 3,
    yesterdaySalesAmount: 3000.0,
    yesterdaySalesCount: 2,
    weekSalesAmount: 15000.0,
    weekSalesCount: 8,
    monthSalesAmount: 45000.0,
    monthSalesCount: 20,
    overdueDebtsAmount: 8000.0,
    overdueDebtsCount: 2,
    overdueCardTypes: {},
    cardTypeQuantities: {
      'كارت شحن': CardTypeQuantity(
        cardType: 'كارت شحن',
        quantity: 10,
        amount: 20000.0,
      ),
      'كارت انترنت': CardTypeQuantity(
        cardType: 'كارت انترنت',
        quantity: 15,
        amount: 30000.0,
      ),
    },
    dueTodayAmount: 2000.0,
    dueTodayCount: 1,
    dueNearAmount: 5000.0,
    dueNearCount: 2,
    paidTodayAmount: 3000.0,
    paidTodayCount: 2,
    paidYesterdayAmount: 1500.0,
    paidYesterdayCount: 1,
    paidWeekAmount: 8000.0,
    paidWeekCount: 5,
    totalPaidCardsCount: 25,
    totalPaidAmount: 45000.0,
    activity: CustomerActivity.active,
    paymentBehavior: PaymentBehavior.onTime,
    lastPaymentDate: DateTime.now().subtract(const Duration(hours: 2)),
    lastPaymentTime: '2:30 مساءً',
    lastPaymentDay: 'الأحد',
    paymentCounter: 12,
  );

  print('✅ تم إنشاء الإحصائيات بنجاح');
  print('📊 إجمالي الديون: ${testStatistics.totalDebtsAmount}');
  print('📦 إجمالي الكارتات: ${testStatistics.totalCardsCount}');
  print('🎯 نشاط العميل: ${testStatistics.activity.displayName}');
  print('💰 سلوك السداد: ${testStatistics.paymentBehavior.displayName}');
  print(
    '🕐 آخر تسديد: ${testStatistics.lastPaymentDay} ${testStatistics.lastPaymentTime}',
  );

  print('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
}
