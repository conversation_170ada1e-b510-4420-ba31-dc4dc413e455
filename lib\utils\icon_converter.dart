import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class IconConverter extends StatelessWidget {
  const IconConverter({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحويل الأيقونة'),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const RepaintBoundary(
              key: GlobalObjectKey('new_app_icon'),
              child: SizedBox(
                width: 512,
                height: 512,
                child: NewAppIconWidget(),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _generateIcon(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 15,
                ),
              ),
              child: const Text('إنشاء أيقونة PNG'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateIcon(BuildContext context) async {
    try {
      final renderObject = context.findRenderObject();
      if (renderObject is! RenderRepaintBoundary) {
        throw Exception('RenderObject is not a RenderRepaintBoundary');
      }
      final RenderRepaintBoundary boundary = renderObject;
      final ui.Image image = await boundary.toImage();
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء الأيقونة بنجاح!')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في إنشاء الأيقونة: $e')));
    }
  }
}

class NewAppIconWidget extends StatelessWidget {
  const NewAppIconWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 512,
      height: 512,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF0F2027), Color(0xFF203A43), Color(0xFF2C5364)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.4),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // دائرة خارجية مع تدرج
          Container(
            width: 400,
            height: 400,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667eea).withValues(alpha: 0.5),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
          ),

          // دائرة بيضاء داخلية
          Container(
            width: 320,
            height: 320,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),

          // أيقونة المحاسبة الرئيسية
          Container(
            width: 240,
            height: 240,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(50),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الحاسبة الرئيسية
                Container(
                  width: 160,
                  height: 160,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    ),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // شاشة الحاسبة
                      Container(
                        width: 120,
                        height: 40,
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2C5364),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Text(
                            '123,456',
                            style: TextStyle(
                              color: Color(0xFF00ff88),
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                      // أزرار الحاسبة
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(15),
                          child: GridView.count(
                            crossAxisCount: 4,
                            mainAxisSpacing: 8,
                            crossAxisSpacing: 8,
                            children: [
                              _buildButton('7'),
                              _buildButton('8'),
                              _buildButton('9'),
                              _buildButton('÷', isOperator: true),
                              _buildButton('4'),
                              _buildButton('5'),
                              _buildButton('6'),
                              _buildButton('×', isOperator: true),
                              _buildButton('1'),
                              _buildButton('2'),
                              _buildButton('3'),
                              _buildButton('=', isEquals: true),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15),
                // نص التطبيق
                const Text(
                  'محاسب ديون احترافي',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF667eea),
                  ),
                ),
              ],
            ),
          ),

          // عملة ذهبية
          Positioned(
            top: 80,
            right: 60,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                ),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFFD700).withValues(alpha: 0.5),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.attach_money,
                color: Colors.white,
                size: 35,
              ),
            ),
          ),

          // بطاقة ائتمان
          Positioned(
            bottom: 80,
            left: 60,
            child: Container(
              width: 80,
              height: 50,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF45a049)],
                ),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white, width: 4),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(
    String text, {
    bool isOperator = false,
    bool isEquals = false,
  }) {
    Color backgroundColor;
    Color textColor;

    if (isEquals) {
      backgroundColor = const Color(0xFF28a745);
      textColor = Colors.white;
    } else if (isOperator) {
      backgroundColor = const Color(0xFF007bff);
      textColor = Colors.white;
    } else {
      backgroundColor = const Color(0xFFf8f9fa);
      textColor = const Color(0xFF495057);
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: const Color(0xFFdee2e6)),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
