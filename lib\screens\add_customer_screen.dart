import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/customer_provider.dart';
import '../widgets/contact_picker_dialog.dart';
import '../services/contacts_service.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key, this.onCustomerAdded});

  final Function(Customer)? onCustomerAdded;

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _clearForm() {
    _nameController.clear();
    _phoneController.clear();
  }

  void _showTopMessage(String message, Color backgroundColor) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  backgroundColor == Colors.green.shade600
                      ? Icons.check_circle
                      : backgroundColor == Colors.red.shade600
                      ? Icons.error
                      : Icons.warning,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة بعد 3 ثوانٍ
    Future.delayed(const Duration(seconds: 3), () {
      overlayEntry.remove();
    });
  }

  void _showContactPicker() async {
    final result = await Navigator.push<ContactInfo>(
      context,
      MaterialPageRoute(
        builder: (context) => ContactPickerScreen(
          onContactSelected: (contactInfo) {
            Navigator.pop(context, contactInfo);
          },
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _nameController.text = result.name;
        _phoneController.text = result.phoneNumber;
      });

      if (mounted) {
        _showTopMessage(
          'تم اختيار ${result.name}: ${result.phoneNumber}',
          Colors.green.shade600,
        );
      }
    }
  }

  void _showMultipleContactPicker() async {
    final results = await Navigator.push<List<ContactInfo>>(
      context,
      MaterialPageRoute(
        builder: (context) => ContactPickerScreen(
          allowMultipleSelection: true,
          onMultipleContactsSelected: (contacts) {
            Navigator.pop(context, contacts);
          },
        ),
      ),
    );

    if (results != null && results.isNotEmpty) {
      await _addMultipleCustomers(results);
    }
  }

  Future<void> _addMultipleCustomers(List<ContactInfo> contacts) async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    int addedCount = 0;
    int skippedCount = 0;

    for (final contact in contacts) {
      // التحقق من عدم تكرار الاسم
      final existingCustomer = customerProvider.customers.firstWhere(
        (customer) => customer.name.toLowerCase() == contact.name.toLowerCase(),
        orElse: () => Customer(
          name: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      if (existingCustomer.name.isNotEmpty) {
        skippedCount++;
        continue;
      }

      try {
        final now = DateTime.now();
        final customer = Customer(
          name: contact.name,
          phone: contact.phoneNumber.isNotEmpty ? contact.phoneNumber : null,
          createdAt: now,
          updatedAt: now,
        );

        await customerProvider.addCustomer(customer);
        addedCount++;
      } catch (e) {
        skippedCount++;
      }
    }

    if (mounted) {
      String message = '';
      if (addedCount > 0 && skippedCount > 0) {
        message = 'تم إضافة $addedCount عميل وتم تخطي $skippedCount عميل مكرر';
      } else if (addedCount > 0) {
        message = 'تم إضافة $addedCount عميل بنجاح';
      } else {
        message = 'جميع الأسماء مكررة - لم يتم إضافة أي عميل';
      }

      _showTopMessage(
        message,
        addedCount > 0 ? Colors.green.shade600 : Colors.orange.shade600,
      );

      // العودة إلى الشاشة السابقة إذا تم إضافة عملاء
      if (addedCount > 0) {
        Navigator.pop(context);
      }
    }
  }

  Future<void> _saveCustomer({bool keepOpen = false}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من تكرار الاسم
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    final existingCustomer = customerProvider.customers.firstWhere(
      (customer) =>
          customer.name.toLowerCase() ==
          _nameController.text.trim().toLowerCase(),
      orElse: () => Customer(
        name: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (existingCustomer.name.isNotEmpty) {
      _showTopMessage('يوجد عميل بنفس الاسم مسبقاً', Colors.orange.shade600);
      return;
    }

    // التحقق من صحة رقم الهاتف إذا تم إدخاله
    final phone = _phoneController.text.trim();
    if (phone.isNotEmpty && phone.length != 11) {
      _showTopMessage(
        'رقم الهاتف يجب أن يكون 11 رقم بالضبط',
        Colors.red.shade600,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final customer = Customer(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        createdAt: now,
        updatedAt: now,
      );

      final addedCustomer = await customerProvider.addCustomer(customer);

      if (mounted) {
        if (widget.onCustomerAdded != null) {
          widget.onCustomerAdded!(addedCustomer);
        }

        _showTopMessage(
          keepOpen
              ? 'تم إضافة العميل بنجاح - يمكنك إضافة عميل آخر'
              : 'تم إضافة العميل بنجاح',
          Colors.green.shade600,
        );

        if (keepOpen) {
          _clearForm();
        } else {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        _showTopMessage('حدث خطأ: ${e.toString()}', Colors.red.shade600);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'إضافة عميل جديد',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.blue.shade50, Colors.white],
            ),
          ),
        ),
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Card
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.blue.shade600,
                              Colors.purple.shade600,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withValues(alpha: 0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.person_add_rounded,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'إضافة عميل جديد',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'أدخل بيانات العميل الجديد',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 30),

                      // Form Fields
                      _buildFormCard(),

                      const SizedBox(height: 30),

                      // Action Buttons
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name Field
          _buildTextField(
            controller: _nameController,
            label: 'اسم العميل',
            icon: Icons.person_rounded,
            iconColor: Colors.blue,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'اسم العميل مطلوب';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // Phone Field with Contact Picker
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف (اختياري)',
                  icon: Icons.phone_rounded,
                  iconColor: Colors.green,
                  keyboardType: TextInputType.phone,
                  maxLength: 11,
                  helperText: 'يجب أن يكون 11 رقم بالضبط',
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: IconButton(
                  onPressed: _showContactPicker,
                  icon: Icon(
                    Icons.contacts_rounded,
                    color: Colors.blue.shade600,
                  ),
                  tooltip: 'اختيار من جهات الاتصال',
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: IconButton(
                  onPressed: _showMultipleContactPicker,
                  icon: Icon(
                    Icons.group_add_rounded,
                    color: Colors.green.shade600,
                  ),
                  tooltip: 'إضافة متعددة من جهات الاتصال',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Color iconColor,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int? maxLength,
    String? helperText,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLength: maxLength,
      validator: validator,
      inputFormatters: inputFormatters,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Colors.black87, // إضافة لون النص بوضوح
      ),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: iconColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: iconColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.red.shade400),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.red.shade400, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        labelStyle: TextStyle(color: Colors.grey.shade600, fontSize: 14),
        counterText: maxLength != null ? '' : null,
        helperText: helperText,
        helperStyle: TextStyle(color: Colors.grey.shade500, fontSize: 12),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Save and Add Another Button
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: _isLoading
                  ? null
                  : () => _saveCustomer(keepOpen: true),
              icon: const Icon(Icons.add_circle_outline_rounded, size: 20),
              label: const Text(
                'حفظ وإضافة آخر',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Save and Close Button
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : () => _saveCustomer(),
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.save_rounded, size: 20),
              label: Text(
                _isLoading ? 'جاري الحفظ...' : 'حفظ وإغلاق',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
