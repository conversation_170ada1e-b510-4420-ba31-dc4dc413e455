import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class CreateAppIcon {
  static Future<Uint8List> generateNewIcon({int size = 512}) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();

    // خلفية متدرجة احترافية
    final bgGradient = ui.Gradient.linear(
      const Offset(0, 0),
      Offset(size.toDouble(), size.toDouble()),
      [
        const Color(0xFF0F2027),
        const Color(0xFF203A43),
        const Color(0xFF2C5364),
      ],
    );

    paint.shader = bgGradient;
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size / 2,
      paint,
    );

    // دائرة خارجية مع تدرج
    final outerGradient = ui.Gradient.linear(
      Offset(size * 0.2, size * 0.2),
      Offset(size * 0.8, size * 0.8),
      [
        const Color(0xFF667eea),
        const Color(0xFF764ba2),
      ],
    );

    paint.shader = outerGradient;
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size * 0.39,
      paint,
    );

    // دائرة داخلية بيضاء
    paint.shader = null;
    paint.color = Colors.white;
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size * 0.31,
      paint,
    );

    // أيقونة المحاسبة الرئيسية
    final centerX = size / 2;
    final centerY = size / 2;
    final iconSize = size * 0.25;

    // خلفية أيقونة الحاسبة
    final iconGradient = ui.Gradient.linear(
      Offset(centerX - iconSize, centerY - iconSize),
      Offset(centerX + iconSize, centerY + iconSize),
      [
        const Color(0xFF667eea),
        const Color(0xFF764ba2),
      ],
    );

    paint.shader = iconGradient;
    final calculatorRect = Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: iconSize * 2,
      height: iconSize * 2,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(calculatorRect, Radius.circular(size * 0.03)),
      paint,
    );

    // شاشة الحاسبة
    paint.shader = null;
    paint.color = const Color(0xFF2C5364);
    final screenRect = Rect.fromCenter(
      center: Offset(centerX, centerY - iconSize * 0.5),
      width: iconSize * 1.5,
      height: iconSize * 0.4,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(screenRect, Radius.circular(size * 0.015)),
      paint,
    );

    // أزرار الحاسبة
    paint.color = Colors.white;
    final buttonSize = iconSize * 0.2;
    final spacing = iconSize * 0.15;

    for (int row = 0; row < 3; row++) {
      for (int col = 0; col < 4; col++) {
        final buttonX = centerX - iconSize * 0.6 + col * spacing;
        final buttonY = centerY + iconSize * 0.1 + row * spacing;

        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromCenter(
              center: Offset(buttonX, buttonY),
              width: buttonSize,
              height: buttonSize,
            ),
            Radius.circular(size * 0.008),
          ),
          paint,
        );
      }
    }

    // عملة ذهبية
    final coinGradient = ui.Gradient.radial(
      Offset(centerX + iconSize * 1.8, centerY - iconSize * 1.8),
      size * 0.06,
      [
        const Color(0xFFFFD700),
        const Color(0xFFFFA500),
      ],
    );
    paint.shader = coinGradient;
    canvas.drawCircle(
      Offset(centerX + iconSize * 1.8, centerY - iconSize * 1.8),
      size * 0.06,
      paint,
    );

    // رمز الدولار على العملة
    paint.shader = null;
    paint.color = Colors.white;
    final dollarPainter = TextPainter(
      text: TextSpan(
        text: '\$',
        style: TextStyle(
          color: Colors.white,
          fontSize: size * 0.04,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    dollarPainter.layout();
    dollarPainter.paint(
      canvas,
      Offset(
        centerX + iconSize * 1.8 - dollarPainter.width / 2,
        centerY - iconSize * 1.8 - dollarPainter.height / 2,
      ),
    );

    // بطاقة ائتمان
    final cardGradient = ui.Gradient.linear(
      Offset(centerX - iconSize * 2, centerY + iconSize * 1.5),
      Offset(centerX - iconSize * 1.2, centerY + iconSize * 2.3),
      [
        const Color(0xFF4CAF50),
        const Color(0xFF45a049),
      ],
    );
    paint.shader = cardGradient;
    final cardRect = Rect.fromCenter(
      center: Offset(centerX - iconSize * 1.6, centerY + iconSize * 1.9),
      width: size * 0.12,
      height: size * 0.08,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(cardRect, Radius.circular(size * 0.008)),
      paint,
    );

    // خط مغناطيسي على البطاقة
    paint.shader = null;
    paint.color = Colors.white;
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(centerX - iconSize * 1.6, centerY + iconSize * 1.7),
        width: size * 0.1,
        height: size * 0.01,
      ),
      paint,
    );

    // نص التطبيق
    final titlePainter = TextPainter(
      text: TextSpan(
        text: 'محاسب ديون',
        style: TextStyle(
          color: Colors.white,
          fontSize: size * 0.055,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    titlePainter.layout();
    titlePainter.paint(
      canvas,
      Offset(
        centerX - titlePainter.width / 2,
        centerY + iconSize * 2.8,
      ),
    );

    final subtitlePainter = TextPainter(
      text: TextSpan(
        text: 'احترافي',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.9),
          fontSize: size * 0.035,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    subtitlePainter.layout();
    subtitlePainter.paint(
      canvas,
      Offset(
        centerX - subtitlePainter.width / 2,
        centerY + iconSize * 3.3,
      ),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // إنشاء أحجام مختلفة للأيقونة
  static Future<Map<String, Uint8List>> generateAllSizes() async {
    final sizes = [36, 48, 72, 96, 144, 192, 512];
    final icons = <String, Uint8List>{};

    for (final size in sizes) {
      icons['app_icon_$size.png'] = await generateNewIcon(size: size);
    }

    return icons;
  }
}
