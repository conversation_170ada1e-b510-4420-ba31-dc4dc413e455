#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Watcher & Auto APK Builder
Professional Debt Accountant
"""

import os
import sys
import time
import subprocess
import threading
import json
from datetime import datetime
from pathlib import Path

class APKBuilder:
    def __init__(self):
        self.project_root = Path.cwd()
        self.last_build_time = 0
        self.build_delay = 10  # Wait 10 seconds after last change
        self.is_building = False
        self.build_timer = None

        # Load configuration
        self.load_config()

        print("🏗️ APK Builder Watcher Ready")
        print(f"📁 Project Directory: {self.project_root}")
        print(f"⏰ Build Delay: {self.build_delay} seconds")
        print("=" * 50)

    def load_config(self):
        """Load configuration from build_config.json"""
        config_file = self.project_root / "build_config.json"

        # Default configuration
        self.watch_patterns = [
            'lib/**/*.dart',
            'assets/**/*',
            'pubspec.yaml',
            'android/app/src/main/AndroidManifest.xml',
            'android/app/build.gradle.kts'
        ]

        self.ignore_patterns = [
            '**/*.g.dart',
            '**/*.freezed.dart',
            '**/generated_plugin_registrant.dart',
            'build/**',
            '.dart_tool/**',
            '.git/**',
            '**/.DS_Store',
            '**/Thumbs.db'
        ]

        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Load file watcher settings
                if 'file_watcher' in config:
                    fw_config = config['file_watcher']
                    self.build_delay = fw_config.get('build_delay_seconds', 10)
                    self.watch_patterns = fw_config.get('watch_patterns', self.watch_patterns)
                    self.ignore_patterns = fw_config.get('ignore_patterns', self.ignore_patterns)

                print("✅ Configuration loaded from build_config.json")
            except Exception as e:
                print(f"⚠️ Could not load config: {e}")
                print("Using default configuration")

    def should_ignore_file(self, file_path):
        """تحديد ما إذا كان يجب تجاهل الملف"""
        file_str = str(file_path).replace('\\', '/')
        
        for pattern in self.ignore_patterns:
            if self.match_pattern(file_str, pattern):
                return True
        return False

    def match_pattern(self, file_path, pattern):
        """مطابقة نمط الملف"""
        import fnmatch
        return fnmatch.fnmatch(file_path, pattern)

    def should_watch_file(self, file_path):
        """تحديد ما إذا كان يجب مراقبة الملف"""
        if self.should_ignore_file(file_path):
            return False
            
        file_str = str(file_path).replace('\\', '/')
        
        for pattern in self.watch_patterns:
            if self.match_pattern(file_str, pattern):
                return True
        return False

    def get_file_modification_time(self, file_path):
        """الحصول على وقت تعديل الملف"""
        try:
            return os.path.getmtime(file_path)
        except:
            return 0

    def build_apk(self):
        """Build APK"""
        if self.is_building:
            print("⏳ Build already in progress...")
            return

        self.is_building = True

        try:
            print("\n" + "=" * 50)
            print(f"🏗️ Starting APK Build - {datetime.now().strftime('%H:%M:%S')}")
            print("=" * 50)

            # Run APK build script
            build_script = self.project_root / "scripts" / "quick_build_apk.bat"

            if build_script.exists():
                print("🔧 Running build script...")

                # Run script in separate window
                if os.name == 'nt':  # Windows
                    subprocess.Popen([
                        'cmd', '/c', 'start', 'cmd', '/k', str(build_script)
                    ], shell=True)
                else:  # Linux/Mac
                    subprocess.Popen([
                        'gnome-terminal', '--', 'bash', str(build_script)
                    ])

                print("✅ Build script started in separate window")
            else:
                print("❌ Build script not found")

        except Exception as e:
            print(f"❌ Error building APK: {e}")
        finally:
            self.is_building = False
            self.last_build_time = time.time()

    def schedule_build(self):
        """Schedule APK build after delay"""
        if self.build_timer:
            self.build_timer.cancel()

        self.build_timer = threading.Timer(self.build_delay, self.build_apk)
        self.build_timer.start()

        print(f"⏰ Build scheduled in {self.build_delay} seconds...")

    def scan_for_changes(self):
        """Scan for file changes"""
        changed_files = []

        for root, dirs, files in os.walk(self.project_root):
            # Ignore excluded directories
            dirs[:] = [d for d in dirs if not any(
                self.match_pattern(f"{root}/{d}", pattern)
                for pattern in self.ignore_patterns
            )]

            for file in files:
                file_path = Path(root) / file

                if self.should_watch_file(file_path):
                    mod_time = self.get_file_modification_time(file_path)

                    if mod_time > self.last_build_time:
                        changed_files.append(file_path)

        return changed_files

    def watch_files(self):
        """Watch files for changes"""
        print("👁️ Starting file monitoring...")
        print("💡 Press Ctrl+C to stop")
        print("\n")

        try:
            while True:
                changed_files = self.scan_for_changes()

                if changed_files:
                    print(f"📝 Changes detected in {len(changed_files)} file(s):")
                    for file_path in changed_files[:5]:  # Show first 5 files only
                        print(f"   📄 {file_path.relative_to(self.project_root)}")

                    if len(changed_files) > 5:
                        print(f"   ... and {len(changed_files) - 5} more files")

                    self.schedule_build()

                time.sleep(2)  # Check every 2 seconds

        except KeyboardInterrupt:
            print("\n🛑 File watcher stopped")
            if self.build_timer:
                self.build_timer.cancel()
        except Exception as e:
            print(f"❌ Error in file watching: {e}")

def main():
    """Main function"""
    try:
        builder = APKBuilder()
        builder.watch_files()
    except Exception as e:
        print(f"❌ General error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
