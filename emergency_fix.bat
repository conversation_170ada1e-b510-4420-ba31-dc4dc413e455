@echo off
echo ========================================
echo    إصلاح طارئ لمشاكل البناء
echo ========================================
echo.

echo 1. فحص Flutter...
flutter --version
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter غير متاح - يرجى تثبيت Flutter أولاً
    echo تحميل من: https://flutter.dev
    pause
    exit /b 1
)

echo.
echo 2. تنظيف شامل للمشروع...
if exist "build" rmdir /s /q "build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"
if exist ".flutter-plugins" del ".flutter-plugins"
if exist ".flutter-plugins-dependencies" del ".flutter-plugins-dependencies"
if exist "pubspec.lock" del "pubspec.lock"

echo.
echo 3. إعادة تحديث التبعيات...
flutter clean
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تحديث التبعيات
    echo جرب: flutter pub cache repair
    pause
    exit /b 1
)

echo.
echo 4. فحص الأخطاء...
flutter analyze
if %ERRORLEVEL% neq 0 (
    echo ⚠️ توجد أخطاء في الكود - يرجى مراجعتها
)

echo.
echo 5. فحص الأجهزة المتاحة...
flutter devices

echo.
echo 6. محاولة تشغيل اختبار بسيط...
echo إنشاء ملف اختبار بسيط...
echo import 'package:flutter/material.dart'; > test_emergency.dart
echo. >> test_emergency.dart
echo void main() { >> test_emergency.dart
echo   runApp(MaterialApp( >> test_emergency.dart
echo     home: Scaffold( >> test_emergency.dart
echo       body: Center( >> test_emergency.dart
echo         child: Text('Flutter يعمل!', style: TextStyle(fontSize: 24)), >> test_emergency.dart
echo       ), >> test_emergency.dart
echo     ), >> test_emergency.dart
echo   )); >> test_emergency.dart
echo } >> test_emergency.dart

echo.
echo تشغيل الاختبار البسيط...
flutter run test_emergency.dart --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تشغيل الاختبار البسيط
    echo المشكلة في إعداد Flutter أو الجهاز
) else (
    echo ✅ الاختبار البسيط نجح - المشكلة في التطبيق الأصلي
)

echo.
echo 7. محاولة تشغيل التطبيق الأصلي...
flutter run --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تشغيل التطبيق الأصلي
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
echo ========================================
echo    انتهى الإصلاح الطارئ
echo ========================================
echo.
echo إذا استمرت المشكلة:
echo 1. تأكد من تثبيت Flutter بشكل صحيح
echo 2. تأكد من وجود جهاز متصل أو محاكي
echo 3. راجع رسائل الخطأ أعلاه
echo 4. جرب: flutter doctor -v
echo.
pause
