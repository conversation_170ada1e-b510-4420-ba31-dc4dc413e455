import 'package:flutter/foundation.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../database/database_helper.dart';
import 'smart_notification_provider.dart';

class DebtProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Debt> _debts = [];
  final List<Debt> _myDebts = []; // الديون التي عليه
  List<Payment> _payments = [];
  bool _isLoading = false;
  int? _currentCustomerId; // Track current customer

  List<Debt> get debts => _debts;
  List<Debt> get myDebts => _myDebts; // الديون التي عليه
  List<Payment> get payments => _payments;
  bool get isLoading => _isLoading;
  int? get currentCustomerId => _currentCustomerId;

  // تحميل جميع الديون
  Future<void> loadAllDebts() async {
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('DebtProvider: Loading all debts');
      final allDebts = await DatabaseHelper().getAllDebts();
      _debts = allDebts;
      debugPrint('DebtProvider: Loaded ${_debts.length} debts');
    } catch (e) {
      debugPrint('DebtProvider: Error loading all debts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadCustomerDebts(int customerId) async {
    _isLoading = true;
    _currentCustomerId = customerId; // Set current customer
    notifyListeners();

    try {
      _debts = await _databaseHelper.getCustomerDebts(customerId);
    } catch (e) {
      debugPrint('Error loading customer debts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل الديون التي يطلبني العميل (أنا أدين للعميل)
  Future<void> loadCustomerOwedDebts(int customerId) async {
    _isLoading = true;
    _currentCustomerId = customerId;
    notifyListeners();

    try {
      _debts = await _databaseHelper.getCustomerOwedDebts(customerId);
    } catch (e) {
      debugPrint('Error loading customer owed debts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل جميع الديون التي عليه (أنا أدين للعملاء)
  Future<void> loadMyDebts() async {
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('DebtProvider: Loading my debts (I owe customers)');
      final allDebts = await DatabaseHelper().getAllDebts();
      _myDebts.clear();
      _myDebts.addAll(
        allDebts.where((debt) => debt.direction == DebtDirection.iOweCustomer),
      );
      debugPrint('DebtProvider: Loaded ${_myDebts.length} my debts');
    } catch (e) {
      debugPrint('DebtProvider: Error loading my debts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadCustomerPayments(int customerId) async {
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('DebtProvider: Loading payments for customer $customerId');
      _payments = await _databaseHelper.getCustomerPayments(customerId);
      debugPrint('DebtProvider: Loaded ${_payments.length} payments');
      if (_payments.isNotEmpty) {
        debugPrint(
          'DebtProvider: Payment types: ${_payments.map((p) => '${p.type.name}(${p.amount})').join(', ')}',
        );
      }
    } catch (e) {
      debugPrint('Error loading customer payments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل جميع المدفوعات
  Future<void> loadAllPayments() async {
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('DebtProvider: Loading all payments');
      _payments = await _databaseHelper.getAllPayments();
      debugPrint('DebtProvider: Loaded ${_payments.length} payments');
    } catch (e) {
      debugPrint('DebtProvider: Error loading all payments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addDebt(Debt debt) async {
    try {
      final id = await _databaseHelper.insertDebt(debt);
      final newDebt = debt.copyWith(id: id);

      // Only add to local list if it belongs to current customer
      if (_currentCustomerId == null || _currentCustomerId == debt.customerId) {
        _debts.insert(0, newDebt);
      }

      // إضافة استقطاع ثابت للديون التي عليك (ديون العملاء)
      if (debt.direction == DebtDirection.iOweCustomer) {
        await _databaseHelper.addPermanentDeduction(
          debt.customerId,
          debt.amount,
          'دين للعميل: ${debt.itemName}',
        );
        debugPrint(
          'تم إضافة استقطاع ثابت بمبلغ ${debt.amount} للعميل ${debt.customerId}',
        );
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error adding debt: $e');
      rethrow;
    }
  }

  Future<void> updateDebt(Debt debt) async {
    try {
      await _databaseHelper.updateDebt(debt);
      final index = _debts.indexWhere((d) => d.id == debt.id);
      if (index != -1) {
        _debts[index] = debt;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating debt: $e');
      rethrow;
    }
  }

  Future<void> deleteDebt(int id) async {
    try {
      await _databaseHelper.deleteDebt(id);
      _debts.removeWhere((debt) => debt.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting debt: $e');
      rethrow;
    }
  }

  Future<void> makePayment(
    int debtId,
    double amount,
    PaymentType type,
    String? notes, {
    DateTime? paymentDate,
  }) async {
    try {
      final debt = _debts.firstWhere((d) => d.id == debtId);

      // Create payment record
      final payment = Payment(
        debtId: debtId,
        customerId: debt.customerId,
        amount: amount,
        type: type,
        notes: notes,
        paymentDate: paymentDate ?? DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final paymentId = await _databaseHelper.insertPayment(payment);
      final savedPayment = payment.copyWith(id: paymentId);

      // Update debt
      final newPaidAmount = debt.paidAmount + amount;
      final newStatus = newPaidAmount >= debt.amount
          ? DebtStatus.paid
          : DebtStatus.partiallyPaid;

      // حفظ تاريخ أول تسديد فقط إذا لم يكن محفوظ من قبل
      final firstPaymentDate =
          debt.firstPaymentDate ?? (paymentDate ?? DateTime.now());

      final updatedDebt = debt.copyWith(
        paidAmount: newPaidAmount,
        status: newStatus,
        firstPaymentDate: firstPaymentDate, // حفظ تاريخ أول تسديد
        // لا نحدث updatedAt للحفاظ على التواريخ الأصلية
      );

      await updateDebt(updatedDebt);

      // Add payment to local list (only once)
      _payments.insert(0, savedPayment);

      debugPrint(
        'Payment added successfully: ID=${savedPayment.id}, Amount=${savedPayment.amount}, Type=${savedPayment.type}',
      );
      debugPrint('Total payments in list: ${_payments.length}');

      // تحديث فوري للتنبيهات عند السداد
      _updateNotificationsAfterPayment(updatedDebt);

      notifyListeners();
    } catch (e) {
      debugPrint('Error making payment: $e');
      rethrow;
    }
  }

  // تحديث التنبيهات فوراً بعد السداد أو التراجع
  void _updateNotificationsAfterPayment(Debt debt) {
    try {
      // البحث عن التنبيه المرتبط بهذا الدين وإزالته إذا تم السداد
      final smartNotificationProvider = SmartNotificationProvider();

      if (debt.status == DebtStatus.paid) {
        // إزالة التنبيهات المرتبطة بهذا الدين المسدد
        smartNotificationProvider.removeNotificationsByDebt(debt.id!);
        debugPrint('🔄 تم إزالة تنبيهات الدين المسدد: ${debt.id}');
      } else {
        // إضافة تنبيه جديد إذا كان الدين لا يزال غير مسدد
        smartNotificationProvider.addDebtNotification(debt);
        debugPrint('🔄 تم إضافة تنبيه للدين غير المسدد: ${debt.id}');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث التنبيهات: $e');
    }
  }

  Future<void> updatePayment(Payment payment) async {
    try {
      if (payment.id == null) {
        throw Exception('معرف التسديد غير صحيح');
      }

      // Find the original payment to calculate difference
      final originalPayment = _payments.firstWhere(
        (p) => p.id == payment.id,
        orElse: () => throw Exception('التسديد غير موجود'),
      );

      // Find debt in current list or fetch from database
      Debt? debt;
      try {
        debt = _debts.firstWhere((d) => d.id == payment.debtId);
      } catch (e) {
        // If debt not found in current list, fetch from database
        debt = await _databaseHelper.getDebt(payment.debtId);
        if (debt != null) {
          _debts.add(debt);
        }
      }

      if (debt == null) {
        throw Exception('الدين المرتبط بهذا التسديد غير موجود');
      }

      // Calculate the difference in payment amount
      final amountDifference = payment.amount - originalPayment.amount;

      // Calculate new paid amount for debt
      final newPaidAmount = (debt.paidAmount + amountDifference).clamp(
        0.0,
        debt.amount,
      );

      final newStatus = newPaidAmount <= 0
          ? DebtStatus.pending
          : newPaidAmount >= debt.amount
              ? DebtStatus.paid
              : DebtStatus.partiallyPaid;

      final updatedDebt = debt.copyWith(
        paidAmount: newPaidAmount,
        status: newStatus,
        // لا نحدث updatedAt للحفاظ على التواريخ الأصلية
      );

      // Update payment in database
      await _databaseHelper.updatePayment(payment);

      // Update debt in database
      await _databaseHelper.updateDebt(updatedDebt);

      // Update payment in local list
      final paymentIndex = _payments.indexWhere((p) => p.id == payment.id);
      if (paymentIndex != -1) {
        _payments[paymentIndex] = payment;
      }

      // Update debt in local list
      final debtIndex = _debts.indexWhere((d) => d.id == debt!.id);
      if (debtIndex != -1) {
        _debts[debtIndex] = updatedDebt;
      }

      // تحديث فوري للتنبيهات عند تحديث السداد
      _updateNotificationsAfterPayment(updatedDebt);

      notifyListeners();
    } catch (e) {
      debugPrint('Error updating payment: $e');
      rethrow;
    }
  }

  Future<void> reversePayment(int paymentId) async {
    try {
      // Find payment
      final payment = _payments.firstWhere(
        (p) => p.id != null && p.id == paymentId,
        orElse: () => throw Exception('التسديد غير موجود'),
      );

      if (payment.id == null) {
        throw Exception('معرف التسديد غير صحيح');
      }

      // Find debt in current list or fetch from database
      Debt? debt;
      try {
        debt = _debts.firstWhere((d) => d.id == payment.debtId);
      } catch (e) {
        // If debt not found in current list, fetch from database
        debt = await _databaseHelper.getDebt(payment.debtId);
        if (debt != null) {
          _debts.add(debt);
        }
      }

      if (debt == null) {
        throw Exception('الدين المرتبط بهذا التسديد غير موجود');
      }

      // Calculate new amounts
      final newPaidAmount = (debt.paidAmount - payment.amount).clamp(
        0.0,
        debt.amount,
      );
      final newStatus = newPaidAmount <= 0
          ? DebtStatus.pending
          : newPaidAmount >= debt.amount
              ? DebtStatus.paid
              : DebtStatus.partiallyPaid;

      final updatedDebt = debt.copyWith(
        paidAmount: newPaidAmount,
        status: newStatus,
        // لا نحدث updatedAt للحفاظ على التواريخ الأصلية
      );

      // Update debt in database
      await _databaseHelper.updateDebt(updatedDebt);

      // Update debt in local list
      final debtIndex = _debts.indexWhere((d) => d.id == debt!.id);
      if (debtIndex != -1) {
        _debts[debtIndex] = updatedDebt;
      }

      // Delete payment from database
      await _databaseHelper.deletePayment(paymentId);

      // Remove payment from local list
      _payments.removeWhere((p) => p.id == paymentId);

      // تحديث فوري للتنبيهات عند التراجع عن السداد
      _updateNotificationsAfterPayment(updatedDebt);

      notifyListeners();
    } catch (e) {
      debugPrint('Error reversing payment: $e');
      rethrow;
    }
  }

  Future<void> deletePayment(int paymentId) async {
    try {
      // Delete payment from database directly without affecting debt
      await _databaseHelper.deletePayment(paymentId);

      // Remove payment from local list
      _payments.removeWhere((p) => p.id == paymentId);

      debugPrint('Payment deleted successfully: ID=$paymentId');
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting payment: $e');
      rethrow;
    }
  }

  List<Debt> getPendingDebts() {
    return _debts.where((debt) => debt.status == DebtStatus.pending).toList();
  }

  List<Debt> getPaidDebts() {
    return _debts.where((debt) => debt.status == DebtStatus.paid).toList();
  }

  List<Debt> getPartiallyPaidDebts() {
    return _debts
        .where((debt) => debt.status == DebtStatus.partiallyPaid)
        .toList();
  }

  List<Debt> getOverdueDebts() {
    return _debts.where((debt) => debt.isOverdue).toList();
  }

  double getTotalDebtAmount() {
    return _debts.fold(0.0, (sum, debt) => sum + debt.amount);
  }

  double getTotalPaidAmount() {
    return _debts.fold(0.0, (sum, debt) => sum + debt.paidAmount);
  }

  double getRemainingAmount() {
    return getTotalDebtAmount() - getTotalPaidAmount();
  }

  // Method to refresh current customer's debts
  Future<void> refreshCurrentCustomerDebts() async {
    if (_currentCustomerId != null) {
      await loadCustomerDebts(_currentCustomerId!);
    }
  }

  // Clear current customer context
  void clearCurrentCustomer() {
    _currentCustomerId = null;
    _debts.clear();
    _payments.clear();
    notifyListeners();
  }

  // أرشفة الدين
  Future<void> archiveDebt(int debtId) async {
    try {
      await _databaseHelper.archiveDebt(debtId);
      _debts.removeWhere((d) => d.id == debtId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error archiving debt: $e');
      rethrow;
    }
  }

  // إلغاء أرشفة الدين
  Future<void> unarchiveDebt(int debtId) async {
    try {
      await _databaseHelper.unarchiveDebt(debtId);
      // إعادة تحميل الديون لإظهار الدين المُلغى أرشفته
      if (_currentCustomerId != null) {
        await loadCustomerDebts(_currentCustomerId!);
      } else {
        await loadAllDebts();
      }
    } catch (e) {
      debugPrint('Error unarchiving debt: $e');
      rethrow;
    }
  }

  // جلب الديون المؤرشفة
  Future<List<Debt>> getArchivedDebts() async {
    try {
      return await _databaseHelper.getArchivedDebts();
    } catch (e) {
      debugPrint('Error getting archived debts: $e');
      return [];
    }
  }
}
