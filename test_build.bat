@echo off
echo ========================================
echo    اختبار بناء التطبيق
echo ========================================
echo.

echo 1. فحص Flutter...
flutter --version
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter غير متاح
    pause
    exit /b 1
)
echo ✅ Flutter متاح

echo.
echo 2. فحص الأجهزة...
flutter devices
echo.

echo 3. تنظيف المشروع...
flutter clean
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تنظيف المشروع
    pause
    exit /b 1
)
echo ✅ تم تنظيف المشروع

echo.
echo 4. تحديث التبعيات...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تحديث التبعيات
    pause
    exit /b 1
)
echo ✅ تم تحديث التبعيات

echo.
echo 5. فحص الأخطاء...
flutter analyze
if %ERRORLEVEL% neq 0 (
    echo ⚠️ توجد أخطاء في الكود
    echo يرجى مراجعة الأخطاء أعلاه
    pause
)

echo.
echo 6. اختبار بناء بسيط...
flutter run simple_test.dart --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تشغيل الاختبار البسيط
    pause
    exit /b 1
)

echo.
echo 7. اختبار التطبيق الأصلي...
flutter run --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل في تشغيل التطبيق الأصلي
    pause
    exit /b 1
)

echo.
echo ✅ تم اجتياز جميع الاختبارات!
pause
