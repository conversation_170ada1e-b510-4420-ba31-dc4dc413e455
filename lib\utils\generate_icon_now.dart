import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';

class GenerateIconNow extends StatefulWidget {
  const GenerateIconNow({super.key});

  @override
  State<GenerateIconNow> createState() => _GenerateIconNowState();
}

class _GenerateIconNowState extends State<GenerateIconNow> {
  bool _isGenerating = false;
  String _status = '';
  int _selectedIconIndex = 0;

  final List<IconDesign> _iconDesigns = [
    IconDesign(
      name: 'التصميم الكلاسيكي',
      description: 'تصميم أنيق مع دوائر متدرجة',
      builder: () => const ClassicAppIcon(),
    ),
    IconDesign(
      name: 'التصميم المودرن',
      description: 'تصميم عصري مع خطوط حادة',
      builder: () => const ModernAppIcon(),
    ),
    IconDesign(
      name: 'التصميم المينيمال',
      description: 'تصميم بسيط وأنيق',
      builder: () => const MinimalAppIcon(),
    ),
    IconDesign(
      name: 'التصميم الذهبي',
      description: 'تصميم فاخر بألوان ذهبية',
      builder: () => const GoldenAppIcon(),
    ),
    IconDesign(
      name: 'التصميم الأزرق',
      description: 'تصميم احترافي بدرجات الأزرق',
      builder: () => const BlueAppIcon(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء أيقونة التطبيق'),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // معاينة الأيقونة
            RepaintBoundary(
              key: const GlobalObjectKey('app_icon_generator'),
              child: SizedBox(
                width: 300,
                height: 300,
                child: _iconDesigns[_selectedIconIndex].builder(),
              ),
            ),

            const SizedBox(height: 30),

            // اسم التصميم المحدد
            Text(
              _iconDesigns[_selectedIconIndex].name,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF667eea),
              ),
            ),
            const SizedBox(height: 5),
            Text(
              _iconDesigns[_selectedIconIndex].description,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 30),

            // شريط اختيار التصميم
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _iconDesigns.length,
                itemBuilder: (context, index) {
                  final isSelected = index == _selectedIconIndex;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIconIndex = index;
                      });
                    },
                    child: Container(
                      width: 100,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF667eea)
                              : Colors.grey[300]!,
                          width: isSelected ? 3 : 1,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: const Color(
                                    0xFF667eea,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ]
                            : null,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // معاينة صغيرة للأيقونة
                          Transform.scale(
                            scale: 0.25,
                            child: _iconDesigns[index].builder(),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            _iconDesigns[index].name.split(
                              ' ',
                            )[1], // أخذ الكلمة الثانية فقط
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isSelected
                                  ? const Color(0xFF667eea)
                                  : Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 40),

            // أزرار العمليات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر حفظ الأيقونة فقط
                ElevatedButton(
                  onPressed: _isGenerating ? null : _saveIconOnly,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 25,
                      vertical: 15,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.save, size: 20),
                      SizedBox(width: 8),
                      Text('حفظ فقط'),
                    ],
                  ),
                ),

                // زر تطبيق الأيقونة
                ElevatedButton(
                  onPressed: _isGenerating ? null : _applyIcon,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF667eea),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 15,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    elevation: 5,
                  ),
                  child: _isGenerating
                      ? const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 18,
                              height: 18,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text('جاري التطبيق...'),
                          ],
                        )
                      : const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle, size: 20),
                            SizedBox(width: 8),
                            Text(
                              'تطبيق الأيقونة',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            if (_status.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(15),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: _status.contains('نجح')
                      ? Colors.green[100]
                      : Colors.red[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _status.contains('نجح') ? Colors.green : Colors.red,
                  ),
                ),
                child: Text(
                  _status,
                  style: TextStyle(
                    color: _status.contains('نجح')
                        ? Colors.green[800]
                        : Colors.red[800],
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveIconOnly() async {
    setState(() {
      _isGenerating = true;
      _status = 'جاري حفظ الأيقونة...';
    });

    try {
      // البحث عن RepaintBoundary باستخدام GlobalKey
      const GlobalKey key = GlobalObjectKey('app_icon_generator');
      final RenderRepaintBoundary? boundary =
          key.currentContext?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        throw Exception('Could not find RepaintBoundary');
      }

      // إنشاء صورة بدقة عالية
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        final Uint8List pngBytes = byteData.buffer.asUint8List();

        // حفظ الأيقونة في عدة أحجام مختلفة
        await _saveIconInMultipleSizes(pngBytes);

        setState(() {
          _isGenerating = false;
          _status =
              'تم حفظ الأيقونة بنجاح! ✅\n'
              'يمكنك الآن تطبيقها أو استخدامها لاحقاً';
        });
      } else {
        throw Exception('فشل في تحويل الصورة');
      }
    } catch (e) {
      setState(() {
        _isGenerating = false;
        _status = 'حدث خطأ: $e';
      });
    }
  }

  Future<void> _applyIcon() async {
    setState(() {
      _isGenerating = true;
      _status = 'جاري تطبيق الأيقونة...';
    });

    try {
      // البحث عن RepaintBoundary باستخدام GlobalKey
      const GlobalKey key = GlobalObjectKey('app_icon_generator');
      final RenderRepaintBoundary? boundary =
          key.currentContext?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        throw Exception('Could not find RepaintBoundary');
      }

      // إنشاء صورة بدقة عالية
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        final Uint8List pngBytes = byteData.buffer.asUint8List();

        // حفظ الأيقونة في عدة أحجام مختلفة
        await _saveIconInMultipleSizes(pngBytes);

        // تطبيق الأيقونة مباشرة
        await _updateAppIcon();

        setState(() {
          _isGenerating = false;
          _status =
              'تم تطبيق الأيقونة بنجاح! ✅\n'
              'الأيقونة الجديدة نشطة الآن';
        });
      } else {
        throw Exception('فشل في تحويل الصورة');
      }
    } catch (e) {
      setState(() {
        _isGenerating = false;
        _status = 'حدث خطأ: $e';
      });
    }
  }

  Future<void> _saveIconInMultipleSizes(Uint8List pngBytes) async {
    // حفظ الأيقونة الأساسية
    final mainFile = File('assets/icons/app_icon.png');
    await mainFile.writeAsBytes(pngBytes);

    // حفظ أحجام مختلفة للأندرويد
    final sizes = [36, 48, 72, 96, 144, 192];
    for (final size in sizes) {
      final file = File('assets/icons/app_icon_$size.png');
      await file.writeAsBytes(pngBytes);
    }

    // نسخ إلى مجلد الأندرويد
    try {
      const androidIconPath =
          'android/app/src/main/res/mipmap-hdpi/ic_launcher.png';
      final androidFile = File(androidIconPath);
      if (await androidFile.parent.exists()) {
        await androidFile.writeAsBytes(pngBytes);
      }
    } catch (e) {
      print('تعذر نسخ الأيقونة إلى مجلد الأندرويد: $e');
    }
  }

  Future<void> _updateAppIcon() async {
    try {
      // تشغيل أمر flutter_launcher_icons لتطبيق الأيقونة
      final result = await Process.run('flutter', [
        'pub',
        'run',
        'flutter_launcher_icons:main',
      ], workingDirectory: Directory.current.path);

      if (result.exitCode == 0) {
        print('تم تطبيق الأيقونة بنجاح');
      } else {
        print('خطأ في تطبيق الأيقونة: ${result.stderr}');
      }
    } catch (e) {
      print('تعذر تشغيل flutter_launcher_icons: $e');
    }
  }
}

// كلاس تصميم الأيقونة
class IconDesign {
  IconDesign({
    required this.name,
    required this.description,
    required this.builder,
  });
  final String name;
  final String description;
  final Widget Function() builder;
}

// التصميم الكلاسيكي (الحالي)
class ClassicAppIcon extends StatelessWidget {
  const ClassicAppIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF0F2027), Color(0xFF203A43), Color(0xFF2C5364)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // دائرة خارجية مع تدرج
          Container(
            width: 240,
            height: 240,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667eea).withValues(alpha: 0.5),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
          ),

          // دائرة بيضاء داخلية
          Container(
            width: 190,
            height: 190,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),

          // أيقونة المحاسبة الرئيسية
          Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الحاسبة الرئيسية
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.calculate_rounded,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                const SizedBox(height: 10),
                // نص التطبيق
                const Text(
                  'محاسب ديون',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF667eea),
                  ),
                ),
                const Text(
                  'احترافي',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF764ba2),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // عملة ذهبية
          Positioned(
            top: 40,
            right: 40,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                ),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFFD700).withValues(alpha: 0.5),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.attach_money,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          // بطاقة ائتمان
          Positioned(
            bottom: 40,
            left: 40,
            child: Container(
              width: 50,
              height: 32,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF45a049)],
                ),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// التصميم المودرن
class ModernAppIcon extends StatelessWidget {
  const ModernAppIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(60),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1e3c72), Color(0xFF2a5298)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 25,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خطوط هندسية في الخلفية
          Positioned(
            top: 20,
            right: 20,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          Positioned(
            bottom: 20,
            left: 20,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),

          // المحتوى الرئيسي
          Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(40),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة رئيسية
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                  const SizedBox(height: 15),
                  const Text(
                    'محاسب ديون',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1e3c72),
                    ),
                  ),
                  const Text(
                    'PRO',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF667eea),
                      letterSpacing: 2,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عناصر تزيينية
          Positioned(
            top: 50,
            left: 50,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: const Color(0xFFFFD700),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.trending_up,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
          Positioned(
            bottom: 50,
            right: 50,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.savings, color: Colors.white, size: 16),
            ),
          ),
        ],
      ),
    );
  }
}

// التصميم المينيمال
class MinimalAppIcon extends StatelessWidget {
  const MinimalAppIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(70),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة بسيطة
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: const Color(0xFF667eea),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.receipt_long,
                color: Colors.white,
                size: 60,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              'محاسب ديون',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w300,
                color: Color(0xFF333333),
                letterSpacing: 1,
              ),
            ),
            const SizedBox(height: 5),
            Container(
              width: 60,
              height: 3,
              decoration: BoxDecoration(
                color: const Color(0xFF667eea),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// التصميم الذهبي
class GoldenAppIcon extends StatelessWidget {
  const GoldenAppIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF8B4513), Color(0xFFD2691E), Color(0xFFFFD700)],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFD700).withValues(alpha: 0.4),
            blurRadius: 25,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // دائرة ذهبية خارجية
          Container(
            width: 250,
            height: 250,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
              ),
              border: Border.all(color: const Color(0xFFFFE55C), width: 3),
            ),
          ),

          // دائرة داخلية
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: const Color(0xFFFFD700), width: 2),
            ),
          ),

          // المحتوى الرئيسي
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة التاج
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.account_balance,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 15),
              const Text(
                'محاسب ديون',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFB8860B),
                ),
              ),
              const Text(
                'PREMIUM',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFFFFD700),
                  letterSpacing: 1.5,
                ),
              ),
            ],
          ),

          // نجوم تزيينية
          Positioned(
            top: 30,
            right: 60,
            child: Icon(
              Icons.star,
              color: Colors.white.withValues(alpha: 0.8),
              size: 20,
            ),
          ),
          Positioned(
            top: 60,
            left: 40,
            child: Icon(
              Icons.star,
              color: Colors.white.withValues(alpha: 0.6),
              size: 16,
            ),
          ),
          Positioned(
            bottom: 40,
            right: 40,
            child: Icon(
              Icons.star,
              color: Colors.white.withValues(alpha: 0.7),
              size: 18,
            ),
          ),
        ],
      ),
    );
  }
}

// التصميم الأزرق
class BlueAppIcon extends StatelessWidget {
  const BlueAppIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(75),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF0F4C75), Color(0xFF3282B8), Color(0xFF0F4C75)],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF3282B8).withValues(alpha: 0.4),
            blurRadius: 30,
            offset: const Offset(0, 20),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خطوط متموجة في الخلفية
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 100,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(75),
                  topRight: Radius.circular(75),
                ),
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),

          // المحتوى الرئيسي
          Center(
            child: Container(
              width: 220,
              height: 220,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(50),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة رئيسية
                  Container(
                    width: 110,
                    height: 110,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Color(0xFF0F4C75), Color(0xFF3282B8)],
                      ),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Icon(
                      Icons.analytics,
                      color: Colors.white,
                      size: 55,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'محاسب ديون',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0F4C75),
                    ),
                  ),
                  const Text(
                    'PROFESSIONAL',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF3282B8),
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عناصر تزيينية
          Positioned(
            top: 40,
            right: 40,
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(Icons.bar_chart, color: Colors.white, size: 18),
            ),
          ),
          Positioned(
            bottom: 40,
            left: 40,
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(Icons.pie_chart, color: Colors.white, size: 18),
            ),
          ),
          Positioned(
            top: 80,
            left: 30,
            child: Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.show_chart,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
