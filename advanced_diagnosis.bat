@echo off
echo ========================================
echo    تشخيص متقدم لمشاكل البناء
echo ========================================
echo.

echo 📋 جمع معلومات النظام...
echo.

echo 1. معلومات Flutter:
echo ----------------------------------------
flutter --version
echo.
flutter doctor -v
echo.

echo 2. فحص المشروع:
echo ----------------------------------------
echo فحص وجود الملفات الأساسية...
if exist "pubspec.yaml" (
    echo ✅ pubspec.yaml موجود
) else (
    echo ❌ pubspec.yaml مفقود
)

if exist "lib\main.dart" (
    echo ✅ lib\main.dart موجود
) else (
    echo ❌ lib\main.dart مفقود
)

if exist "android" (
    echo ✅ مجلد android موجود
) else (
    echo ❌ مجلد android مفقود
)

echo.
echo 3. فحص مساحة القرص:
echo ----------------------------------------
dir | find "bytes free"

echo.
echo 4. فحص العمليات النشطة:
echo ----------------------------------------
tasklist | find "flutter"
tasklist | find "dart"

echo.
echo 5. اختبار الأوامر الأساسية:
echo ----------------------------------------
echo اختبار flutter clean...
flutter clean
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل flutter clean
) else (
    echo ✅ نجح flutter clean
)

echo.
echo اختبار flutter pub get...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل flutter pub get
    echo محاولة إصلاح cache...
    flutter pub cache repair
    flutter pub get
) else (
    echo ✅ نجح flutter pub get
)

echo.
echo 6. اختبار البناء:
echo ----------------------------------------
echo اختبار flutter analyze...
flutter analyze
if %ERRORLEVEL% neq 0 (
    echo ❌ توجد أخطاء في الكود
) else (
    echo ✅ لا توجد أخطاء في الكود
)

echo.
echo 7. اختبار الأجهزة:
echo ----------------------------------------
flutter devices
if %ERRORLEVEL% neq 0 (
    echo ❌ مشكلة في فحص الأجهزة
) else (
    echo ✅ تم فحص الأجهزة بنجاح
)

echo.
echo 8. اختبار التطبيق البديل:
echo ----------------------------------------
echo تشغيل backup_main.dart...
flutter run backup_main.dart --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل التطبيق البديل - مشكلة في Flutter
) else (
    echo ✅ نجح التطبيق البديل - المشكلة في التطبيق الأصلي
)

echo.
echo 9. اختبار التطبيق الأصلي:
echo ----------------------------------------
echo تشغيل lib/main.dart...
flutter run lib/main.dart --debug --verbose
if %ERRORLEVEL% neq 0 (
    echo ❌ فشل التطبيق الأصلي
) else (
    echo ✅ نجح التطبيق الأصلي
)

echo.
echo ========================================
echo    ملخص التشخيص
echo ========================================
echo.
echo إذا نجح التطبيق البديل وفشل الأصلي:
echo - المشكلة في كود التطبيق الأصلي
echo - راجع أخطاء flutter analyze
echo - تحقق من التبعيات في pubspec.yaml
echo.
echo إذا فشل كلاهما:
echo - المشكلة في إعداد Flutter
echo - راجع نتائج flutter doctor
echo - تأكد من وجود جهاز متصل
echo.
pause
