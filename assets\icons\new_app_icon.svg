<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0F2027;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#203A43;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2C5364;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outerRing" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45a049;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filters -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="15" flood-color="#000000" flood-opacity="0.4"/>
    </filter>
    <filter id="iconShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    <filter id="goldShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="6" flood-color="#FFD700" flood-opacity="0.5"/>
    </filter>
    <filter id="greenShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#4CAF50" flood-opacity="0.5"/>
    </filter>
  </defs>
  
  <!-- Background circle with shadow -->
  <circle cx="256" cy="256" r="256" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Outer gradient ring -->
  <circle cx="256" cy="256" r="200" fill="url(#outerRing)" filter="url(#iconShadow)"/>
  
  <!-- Inner white circle -->
  <circle cx="256" cy="256" r="160" fill="white"/>
  
  <!-- Main calculator icon container -->
  <rect x="196" y="166" width="120" height="120" rx="25" fill="white" filter="url(#iconShadow)"/>
  
  <!-- Calculator icon -->
  <rect x="216" y="186" width="80" height="80" rx="15" fill="url(#iconGradient)"/>
  
  <!-- Calculator screen -->
  <rect x="226" y="196" width="60" height="20" rx="4" fill="#2C5364"/>
  
  <!-- Screen text -->
  <text x="256" y="209" text-anchor="middle" fill="#00ff88" font-family="Arial, sans-serif" font-size="10" font-weight="bold">123,456</text>
  
  <!-- Calculator buttons grid -->
  <g fill="white">
    <!-- Row 1 -->
    <rect x="230" y="225" width="12" height="12" rx="2"/>
    <rect x="246" y="225" width="12" height="12" rx="2"/>
    <rect x="262" y="225" width="12" height="12" rx="2"/>
    <rect x="278" y="225" width="12" height="12" rx="2"/>
    
    <!-- Row 2 -->
    <rect x="230" y="241" width="12" height="12" rx="2"/>
    <rect x="246" y="241" width="12" height="12" rx="2"/>
    <rect x="262" y="241" width="12" height="12" rx="2"/>
    <rect x="278" y="241" width="12" height="12" rx="2"/>
    
    <!-- Row 3 -->
    <rect x="230" y="257" width="12" height="12" rx="2"/>
    <rect x="246" y="257" width="12" height="12" rx="2"/>
    <rect x="262" y="257" width="12" height="12" rx="2"/>
    <rect x="278" y="257" width="12" height="12" rx="2"/>
  </g>
  
  <!-- Button numbers -->
  <g fill="#667eea" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle">
    <text x="236" y="234">7</text>
    <text x="252" y="234">8</text>
    <text x="268" y="234">9</text>
    <text x="284" y="234">÷</text>
    
    <text x="236" y="250">4</text>
    <text x="252" y="250">5</text>
    <text x="268" y="250">6</text>
    <text x="284" y="250">×</text>
    
    <text x="236" y="266">1</text>
    <text x="252" y="266">2</text>
    <text x="268" y="266">3</text>
    <text x="284" y="266">=</text>
  </g>
  
  <!-- Gold coin with shadow -->
  <circle cx="370" cy="170" r="30" fill="url(#goldGradient)" stroke="white" stroke-width="3" filter="url(#goldShadow)"/>
  <text x="370" y="178" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">$</text>
  
  <!-- Credit card with shadow -->
  <rect x="120" y="340" width="50" height="32" rx="6" fill="url(#greenGradient)" stroke="white" stroke-width="3" filter="url(#greenShadow)"/>
  <rect x="126" y="350" width="38" height="4" fill="white" opacity="0.8"/>
  <g fill="white">
    <circle cx="132" cy="362" r="2"/>
    <circle cx="138" cy="362" r="2"/>
    <circle cx="144" cy="362" r="2"/>
    <circle cx="150" cy="362" r="2"/>
  </g>
  
  <!-- App title with gradient -->
  <text x="256" y="400" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="28" font-weight="bold">محاسب ديون</text>
  <text x="256" y="430" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" opacity="0.9">احترافي</text>
  
  <!-- Decorative elements -->
  <circle cx="150" cy="150" r="4" fill="white" opacity="0.3"/>
  <circle cx="380" cy="380" r="6" fill="white" opacity="0.2"/>
  <circle cx="100" cy="300" r="3" fill="white" opacity="0.4"/>
  <circle cx="400" cy="250" r="5" fill="white" opacity="0.3"/>
</svg>
