class CardProfit {

  CardProfit({
    this.id,
    required this.cardType,
    required this.costPrice,
    required this.sellingPrice,
    required this.quantity,
    required this.date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : profit = sellingPrice - costPrice,
       totalProfit = (sellingPrice - costPrice) * quantity,
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory CardProfit.fromMap(Map<String, dynamic> map) {
    return CardProfit(
      id: map['id']?.toInt(),
      cardType: map['card_type'] ?? '',
      costPrice: (map['cost_price'] ?? 0).toDouble(),
      sellingPrice: (map['selling_price'] ?? 0).toDouble(),
      quantity: map['quantity']?.toInt() ?? 0,
      date: DateTime.parse(map['date']),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
  final int? id;
  final String cardType;
  final double costPrice;    // سعر الشراء
  final double sellingPrice; // سعر البيع
  final double profit;       // الربح
  final int quantity;        // الكمية المباعة
  final double totalProfit;  // إجمالي الربح
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'card_type': cardType,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'profit': profit,
      'quantity': quantity,
      'total_profit': totalProfit,
      'date': date.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CardProfit copyWith({
    int? id,
    String? cardType,
    double? costPrice,
    double? sellingPrice,
    int? quantity,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CardProfit(
      id: id ?? this.id,
      cardType: cardType ?? this.cardType,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      quantity: quantity ?? this.quantity,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CardProfit{id: $id, cardType: $cardType, profit: $profit, totalProfit: $totalProfit}';
  }
}
