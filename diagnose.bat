@echo off
echo ========================================
echo    تشخيص مشاكل البناء
echo ========================================
echo.

echo 1. فحص Flutter...
flutter --version
echo.

echo 2. فحص pubspec.yaml...
if exist "pubspec.yaml" (
    echo ✅ pubspec.yaml موجود
) else (
    echo ❌ pubspec.yaml مفقود
    pause
    exit /b 1
)

echo.
echo 3. فحص lib/main.dart...
if exist "lib\main.dart" (
    echo ✅ lib/main.dart موجود
) else (
    echo ❌ lib/main.dart مفقود
    pause
    exit /b 1
)

echo.
echo 4. تنظيف المشروع...
flutter clean

echo.
echo 5. تحديث التبعيات...
flutter pub get

echo.
echo 6. فحص الأخطاء...
flutter analyze

echo.
echo 7. اختبار بناء بسيط جداً...
flutter run minimal_test.dart --debug

echo.
echo 8. اختبار البناء العادي...
flutter build apk --debug

echo.
echo ========================================
echo    انتهى التشخيص
echo ========================================
pause
